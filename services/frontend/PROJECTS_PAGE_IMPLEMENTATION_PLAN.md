# Projects Page Implementation Plan

## Overview
This document outlines the comprehensive plan for implementing a new Projects page in the AtharEMS frontend application. The implementation will follow existing patterns and integrate with the Core API for project management.

## 1. Backend Permissions Setup

### 1.1 Update Permission Enums (Frontend)
**File:** `services/frontend/src/enums/Permission.ts`

Add project-related permissions:
```typescript
// Add these to PermissionEnum
MANAGE_PROJECT = "manage:project",
READ_PROJECT = "read:project", 
CREATE_PROJECT = "create:project",
UPDATE_PROJECT = "update:project",
DESTROY_PROJECT = "destroy:project",
```

### 1.2 Verify Core Permissions (Backend)
**File:** `services/core/db/seeds/03_permissions.rb`

The following permissions already exist (lines 136-140):
- Manage Projects (manage:project)
- Read Projects (read:project) 
- Create Project (create:project)
- Update Project (update:project)
- Destroy Project (destroy:project)

✅ **No backend changes needed for permissions**

## 2. Navigation and Menu Integration

### 2.1 Add Projects to Core System Menu
**File:** `services/frontend/src/constants/index.ts`

Add core system to `getSidebarData()`:
```typescript
core: {
  head: "AtharCore",
  icon: "Settings", // or appropriate icon
  items: [
    {
      title: t(`common.sidebar.links.core`),
      url: `/${SYSTEM.CORE}`,
      icon: "SquaresFour",
      permission: [],
    },
    {
      title: t("common.sidebar.links.projects"),
      url: `/${SYSTEM.CORE}/projects`,
      icon: "FolderOpen", // or appropriate icon
      permission: [
        PermissionEnum.MANAGE_PROJECT,
        PermissionEnum.READ_PROJECT,
        PermissionEnum.CREATE_PROJECT,
        PermissionEnum.UPDATE_PROJECT,
      ],
    },
  ],
},
```

### 2.2 Update System Constants
**File:** `services/frontend/src/constants/enum.ts`

Add CORE system:
```typescript
export enum SYSTEM {
  PEOPLE = "people",
  PROCURE = "procure", 
  CM = "cm",
  CORE = "core", // Add this
}
```

### 2.3 Update Types
**File:** `services/frontend/src/types/index.ts`

Update TSystems type:
```typescript
export type TSystems = "core" | "people" | "procure" | "cm";
```

### 2.4 Add Core System to Systems Array
**File:** `services/frontend/src/constants/index.ts`

Add core system to `getSystemsArr()`:
```typescript
{
  href: `/${SYSTEM.CORE}`,
  name: `${SYSTEM.CORE}`,
  display_Name: t(`auth.selectSystem.core.title`),
  title: "AtharCore",
  description: t(`auth.selectSystem.core.description`),
  img: "/images/systems-icons/AtharCore.svg",
  alt: t(`auth.selectSystem.core.alt`),
},
```

## 3. Translation Updates

### 3.1 English Translations
**File:** `services/frontend/messages/en.json`

Add to `common.sidebar.links`:
```json
"core": "Home",
"projects": "Projects"
```

Add new section:
```json
"projects": {
  "page": {
    "title": "Projects",
    "description": "Manage and organize your projects",
    "table": {
      "title": "All Projects",
      "empty": {
        "title": "No projects found",
        "description": "Create your first project to get started"
      },
      "columns": {
        "name": "Project Name",
        "description": "Description", 
        "status": "Status",
        "actions": "Actions"
      }
    },
    "create": {
      "title": "Create Project",
      "success": "Project created successfully",
      "error": "Failed to create project"
    },
    "update": {
      "title": "Update Project",
      "success": "Project updated successfully", 
      "error": "Failed to update project"
    },
    "delete": {
      "success": "Project deleted successfully",
      "error": "Failed to delete project"
    }
  }
}
```

### 3.2 Arabic Translations  
**File:** `services/frontend/messages/ar.json`

Add corresponding Arabic translations:
```json
"core": "الرئيسية",
"projects": "المشاريع"
```

Add Arabic project section with appropriate translations.

## 4. API Integration

### 4.1 Enhanced Core API Service
**File:** `services/frontend/src/services/api/core.ts`

Add CRUD methods:
```typescript
// Create project
async createProject(data: CreateProjectData): Promise<TProjectResponse> {
  const coreToken = await getCoreSessionToken();
  return this.request<TProjectResponse>("api/projects", {
    headers: {
      Authorization: `Bearer ${coreToken}`,
      "Content-Type": "application/json",
    },
    method: "POST",
    body: JSON.stringify({ data }),
  });
}

// Update project  
async updateProject(id: string, data: UpdateProjectData): Promise<TProjectResponse> {
  const coreToken = await getCoreSessionToken();
  return this.request<TProjectResponse>(`api/projects/${id}`, {
    headers: {
      Authorization: `Bearer ${coreToken}`,
      "Content-Type": "application/json", 
    },
    method: "PATCH",
    body: JSON.stringify({ data }),
  });
}

// Delete project
async deleteProject(id: string): Promise<void> {
  const coreToken = await getCoreSessionToken();
  return this.request<void>(`api/projects/${id}`, {
    headers: {
      Authorization: `Bearer ${coreToken}`,
    },
    method: "DELETE",
  });
}

// Get single project
async getProject(id: string): Promise<TProjectResponse> {
  const coreToken = await getCoreSessionToken();
  return this.request<TProjectResponse>(`api/projects/${id}`, {
    headers: {
      Authorization: `Bearer ${coreToken}`,
    },
    method: "GET",
  });
}
```

### 4.2 Enhanced API Routes
**File:** `services/frontend/src/app/api/projects/route.ts`

Add POST method:
```typescript
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const response = await coreAPI.createProject(body);
    return NextResponse.json(response);
  } catch (error) {
    console.error("Error creating project:", error);
    const errorMessage = error instanceof Error ? error.message : "Unknown error";
    return NextResponse.json(
      { error: "Failed to create project", message: errorMessage },
      { status: 500 }
    );
  }
}
```

**File:** `services/frontend/src/app/api/projects/[id]/route.ts` (New file)

Add individual project CRUD operations.

## 5. Type Definitions

### 5.1 Enhanced Project Types
**File:** `services/frontend/src/types/core/project.ts`

Add form and mutation types:
```typescript
export type CreateProjectData = {
  type: "project";
  attributes: {
    name: string;
    description?: string;
    status?: "active" | "inactive";
  };
};

export type UpdateProjectData = CreateProjectData & {
  id: string;
};

export type TProjectResponse = {
  data: TProject;
};
```

## 6. Page Structure Implementation

### 6.1 Main Projects Page
**File:** `services/frontend/src/app/[locale]/core/projects/page.tsx`

```typescript
import React, { Suspense } from "react";
import ProjectsHeader from "../../_modules/core/_components/projects/header/projects-header";
import ProjectsTable from "../../_modules/core/_components/projects/table";
import { getTranslations } from "next-intl/server";

const Projects = async ({
  searchParams,
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) => {
  const params = await searchParams;
  const t = await getTranslations("projects.page");

  return (
    <>
      <ProjectsHeader title={t("title")} />
      <Suspense fallback={<div>{"Loading..."}</div>}>
        <ProjectsTable showPagination={true} searchParams={params} />
      </Suspense>
    </>
  );
};

export default Projects;
```

### 6.2 Core System Layout
**File:** `services/frontend/src/app/[locale]/core/layout.tsx`

```typescript
import SystemLayout from "@/components/sidebar/system-layout";

export default function CoreLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <SystemLayout system="core">{children}</SystemLayout>;
}
```

### 6.3 Core Home Page
**File:** `services/frontend/src/app/[locale]/core/page.tsx`

Basic dashboard for core system.

## 7. Component Implementation

### 7.1 Projects Table Component
**File:** `services/frontend/src/app/[locale]/_modules/core/_components/projects/table/index.tsx`

Follow the pattern from employees table with:
- Data fetching using SWR
- Pagination support
- Search functionality
- CRUD operations
- Permission-based actions

### 7.2 Projects Table Columns
**File:** `services/frontend/src/app/[locale]/_modules/core/_components/projects/table/projects-columns.tsx`

Define table columns with actions menu.

### 7.3 Projects Header Component
**File:** `services/frontend/src/app/[locale]/_modules/core/_components/projects/header/projects-header.tsx`

Header with create button and description.

### 7.4 Project Form Components
Create form components for create/edit operations following existing patterns.

## 8. Hooks Implementation

### 8.1 Projects Data Hook
**File:** `services/frontend/src/app/[locale]/_modules/core/hooks/projects/useProjects.ts`

Enhanced version of existing hook with pagination and filtering.

### 8.2 Project Mutations Hook  
**File:** `services/frontend/src/app/[locale]/_modules/core/hooks/projects/useProjectMutations.ts`

Handle CRUD operations with optimistic updates.

## 9. Implementation Priority

1. **Phase 1: Foundation** (Backend permissions, navigation, types)
2. **Phase 2: API Integration** (Enhanced API methods, routes)
3. **Phase 3: Core Components** (Table, forms, hooks)
4. **Phase 4: Pages** (Main page, layouts)
5. **Phase 5: Testing & Polish** (Translations, error handling)

## 10. Notes

- Follow existing patterns from employees/devices pages
- Ensure proper permission checks throughout
- Use consistent styling and components
- Implement proper error handling and loading states
- Add comprehensive translations for both languages
- Follow JSON:API specification for API calls
