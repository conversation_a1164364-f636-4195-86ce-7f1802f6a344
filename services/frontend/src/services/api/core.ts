import { ActionState, TSystems } from "@/types";
import { BaseAPI, getCoreSessionToken, getMainToken } from "./index";
import {
  ApiUserResponse,
  LoginResponse,
  SessionResponse,
  TUser,
} from "@/types/auth";
import { TProjectsResponse, TProjectResponse, CreateProjectData, UpdateProjectData } from "@/types/core/project";
import { TRolesResponse } from "@/types/core/role";
import { cookies } from "next/headers";
import { handleError } from "@/lib/utils";
import { buildApiUrl } from "@/utils/api";
import { SYSTEM } from "@/constants/enum";

export class CoreAPI extends BaseAPI {
  constructor() {
    super(buildApiUrl("core"));
  }

  async login(email: string, password: string): Promise<LoginResponse> {
    return this.request("api/auth/token", {
      method: "POST",
      body: JSON.stringify({ user: { email, password } }),
    });
  }

  async getCoreSession(): Promise<SessionResponse> {
    const mainToken = await getMainToken();
    return this.request<SessionResponse>("api/auth/session_token", {
      method: "POST",
      headers: {
        Authorization: `Bearer ${mainToken}`,
      },
      body: JSON.stringify({ scope: "core" }),
    });
  }

  async getSystemSession(scope: TSystems): Promise<SessionResponse> {
    const mainToken = await getMainToken();
    return this.request<SessionResponse>("api/auth/session_token", {
      method: "POST",
      headers: {
        Authorization: `Bearer ${mainToken}`,
      },
      body: JSON.stringify({ scope }),
    });
  }

  async getUser(): Promise<TUser> {
    const coreToken = await getCoreSessionToken();
    const response = await this.request<ApiUserResponse>("api/users/me", {
      headers: {
        Authorization: `Bearer ${coreToken}`,
      },
    });

    const userData = response.data.attributes;

    // Add systems from meta.allowed_systems
    if (response.meta?.allowed_systems) {
      userData.systems = response.meta.allowed_systems;
    }

    return userData;
  }

  // Fetch user permissions for a given scope
  async getPermissions({ scope = "core" }: { scope: TSystems }) {
    const coreSessionToken = await getCoreSessionToken();

    const response = await this.request<any>(
      `/api/users/me/permissions?scope=${scope}`,
      {
        headers: {
          Authorization: `Bearer ${coreSessionToken}`,
          "Content-Type": "application/json",
        },
        method: "GET",
      },
    );

    return response;
  }

  async updatePassword(
    currentPassword: string,
    newPassword: string,
    confirmPassword: string,
  ): Promise<{ meta: { message: string } }> {
    const coreToken = await getCoreSessionToken();

    return this.request<{ meta: { message: string } }>(
      "api/users/me/password",
      {
        method: "PATCH",
        headers: {
          Authorization: `Bearer ${coreToken}`,
        },
        body: JSON.stringify({
          current_password: currentPassword,
          password: newPassword,
          password_confirmation: confirmPassword,
        }),
      },
    );
  }

  async updateUserInfo(formData: FormData): Promise<TUser> {
    const coreToken = await getCoreSessionToken();
    const response = await this.requestWithFormData<ApiUserResponse>(
      "api/users/me",
      {
        method: "PATCH",
        headers: {
          Authorization: `Bearer ${coreToken}`,
        },
        body: formData,
      },
    );

    return response.data.attributes;
  }

  async logout<T extends { redirectTo: string }>(): Promise<ActionState<T>> {
    try {
      const cookieStore = await cookies();
      const lang = cookieStore.get("NEXT_LOCALE")?.value || "en";

      // Clear all auth-related cookies
      cookieStore.delete("core_session_token");
      cookieStore.delete("main_token");
      cookieStore.delete("currSystem");
      if (typeof window !== "undefined") {
        sessionStorage.removeItem("user_permissions");
      }

      // Clear any system-specific tokens
      Object.values(SYSTEM).forEach((system) => {
        cookieStore.delete(`${system}_session_token`);
      });

      return {
        success: "Logged out successfully",
        error: "",
        issues: [],
        redirectTo: `/${lang}/auth/login`,
      };
    } catch (error) {
      return handleError(error, "Logout failed");
    }
  }

  // Get all projects
  async getProjects(): Promise<TProjectsResponse> {
    const coreToken = await getCoreSessionToken();
    return this.request<TProjectsResponse>("api/projects", {
      headers: {
        Authorization: `Bearer ${coreToken}`,
      },
      method: "GET",
    });
  }

  // Get all roles with optional sorting
  async getRoles(sort: string = "-id"): Promise<TRolesResponse> {
    const coreToken = await getCoreSessionToken();
    return this.request<TRolesResponse>(`api/roles?sort=${sort}`, {
      headers: {
        Authorization: `Bearer ${coreToken}`,
      },
      method: "GET",
    });
  }

  // Get single project
  async getProject(id: string): Promise<TProjectResponse> {
    const coreToken = await getCoreSessionToken();
    return this.request<TProjectResponse>(`api/projects/${id}`, {
      headers: {
        Authorization: `Bearer ${coreToken}`,
      },
      method: "GET",
    });
  }

  // Create project
  async createProject(data: CreateProjectData): Promise<TProjectResponse> {
    const coreToken = await getCoreSessionToken();
    return this.request<TProjectResponse>("api/projects", {
      headers: {
        Authorization: `Bearer ${coreToken}`,
        "Content-Type": "application/json",
      },
      method: "POST",
      body: JSON.stringify({ data }),
    });
  }

  // Update project
  async updateProject(id: string, data: UpdateProjectData): Promise<TProjectResponse> {
    const coreToken = await getCoreSessionToken();
    return this.request<TProjectResponse>(`api/projects/${id}`, {
      headers: {
        Authorization: `Bearer ${coreToken}`,
        "Content-Type": "application/json",
      },
      method: "PATCH",
      body: JSON.stringify({ data }),
    });
  }

  // Delete project
  async deleteProject(id: string): Promise<void> {
    const coreToken = await getCoreSessionToken();
    return this.request<void>(`api/projects/${id}`, {
      headers: {
        Authorization: `Bearer ${coreToken}`,
      },
      method: "DELETE",
    });
  }
}

// Create a singleton instance
export const coreAPI = new CoreAPI();
