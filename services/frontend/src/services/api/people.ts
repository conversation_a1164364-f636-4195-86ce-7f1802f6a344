import { buildApiUrl } from "@/utils/api";
import { BaseAPI, getUserLanguage } from ".";

import {
  TEmployee,
  TEmployeeAttachment,
  TEmployeeAttachmentsResponse,
  TEmployeeResponse,
  TEmployeesResponse,
} from "@/app/[locale]/_modules/people/type/employee";
import {
  AttendanceEventResponse,
  EmployeeLeavesResponse,
} from "@/app/[locale]/_modules/people/type/employee-leaves";
import { ApprovalRequestResponse } from "@/app/[locale]/_modules/people/type/approval-request";
import { TSalaryPackageResponse } from "@/app/[locale]/_modules/people/type/salary-package";
import { cookies, headers } from "next/headers";
import { MetricCardResponse } from "@/types";
import { format, startOfMonth } from "date-fns";
import { THolidaysResponse } from "@/types/settings/holidays";
import {
  TDevice,
  TDeviceResponse,
} from "@/app/[locale]/_modules/people/type/devices/device";
import {
  TDeviceUsersResponse,
  TDeviceUserResponse,
  TDeviceUserMappingResponse,
  TAvailableCommandsResponse,
  TCommandExecutionResponse,
} from "@/app/[locale]/_modules/people/type/devices/usersList";



// Define a custom error type that includes status code
type ApiError = Error & {
  status?: number;
};

/**
 * PeopleService class for handling all people-related API requests
 */
export class PeopleService extends BaseAPI {
  constructor() {
    super(buildApiUrl("people"));
  }

  // Get all employees with pagination and sorting
  async getEmployees(
    page: number = 1,
    limit: number = 5,
    sort: string = "start_date",
    search: string = "",
    filters: string = "",
  ) {
    const peoplesessionToken = await getPeopleSessionToken();

    let url = `/api/employees?sort=${sort}&page[number]=${page}&page[size]=${limit}`;
    if (search) {
      url += `&filter[search]=${encodeURIComponent(search)}`;
    }
    if (filters) {
      url += `&${filters}`; //for modal filters
    }

    const response = await this.request<TEmployeesResponse>(url, {
      headers: { Authorization: `Bearer ${peoplesessionToken}` },
      method: "GET",
    });

    return response;
  }

  // Get a specific employee by ID
  async getEmployeeById(id: string, include?: string) {
    const peoplesessionToken = await getPeopleSessionToken();

    let url = `/api/employees/${id}`;
    if (include) {
      url += `?include=${include}`;
    }

    const response = await this.request<TEmployeeResponse>(url, {
      headers: { Authorization: `Bearer ${peoplesessionToken}` },
      method: "GET",
    });

    return response;
  }

  // Get Day Summary for employee
  async getDaySummary(id: string, date: string) {
    const peoplesessionToken = await getPeopleSessionToken();

    const response = await this.request<TEmployeeResponse>(
      `/api/attendance/periods/daily_records?date=${date}&employee_id=${id}`,
      {
        headers: {
          Authorization: `Bearer ${peoplesessionToken}`,
          ContentType: "application/json",
        },
        method: "GET",
      },
    );

    return response;
  }

  // Get employee attachments
  async getEmployeeAttachments(employeeId: string) {
    const peoplesessionToken = await getPeopleSessionToken();
    const response = await this.request<TEmployeeAttachmentsResponse>(
      `/api/employees/${employeeId}/attachment`,
      {
        headers: { Authorization: `Bearer ${peoplesessionToken}` },
        method: "GET",
      },
    );
    return response;
  }

  async getStatistics(
    employeeId?: string, // optional - if provided: employee stats, if omitted: global stats
    metricArray: string[] = [],
    period: string = "month",
    startDate: string = format(startOfMonth(new Date()), "dd-MM-yyyy"),
    endDate?: string,
  ) {
    const peopleSessionToken = await getPeopleSessionToken();

    const params = new URLSearchParams({
      [`context[comparison_period]`]: period,
      [`context[start_date]`]: startDate,
    });

    // Only add employee_id if provided (for employee-specific stats)
    if (employeeId) {
      params.append("context[employee_id]", employeeId);
    }

    if (endDate) {
      params.append("context[end_date]", endDate);
    }

    metricArray.forEach((metric) => {
      params.append("filter[metric_key_in][]", metric);
    });

    const response = await this.request<MetricCardResponse>(
      `/api/statistics?${params.toString()}`,
      {
        headers: { Authorization: `Bearer ${peopleSessionToken}` },
        method: "GET",
      },
    );
    return response;
  }

  //  Rename an employee attachment
  async renameAttachment(
    employeeId: string,
    attachmentId: string,
    newName: string,
  ) {
    type RenameResponse = {
      data: TEmployeeAttachment;
    };

    const formData = new FormData();
    formData.append("attachment[filename]", newName);
    const peoplesessionToken = await getPeopleSessionToken();

    const response = await this.requestWithFormData<RenameResponse>(
      `/api/employees/${employeeId}/attachment/${attachmentId}`,
      {
        headers: { Authorization: `Bearer ${peoplesessionToken}` },
        method: "PUT",
        body: formData,
      },
    );
    return response.data;
  }

  // Create an employee attachment
  async createAttachment(employeeId: string, formData: FormData) {
    try {
      const peoplesessionToken = await getPeopleSessionToken();
      const apiFormData = new FormData();
      // Transform the form data to match the API format
      for (const [key, value] of formData.entries()) {
        if (key === "attachments[]") {
          apiFormData.append("attachment", value);
        }
      }

      const response = await this.requestWithFormData(
        `/api/employees/${employeeId}/attachment`,
        {
          headers: { Authorization: `Bearer ${peoplesessionToken}` },
          method: "POST",
          body: apiFormData,
        },
      );
      return response;
    } catch (error) {
      throw error;
    }
  }

  // Delete an employee attachment
  async deleteAttachment(employeeId: string, attachmentId: string) {
    const peoplesessionToken = await getPeopleSessionToken();
    await this.request(
      `api/employees/${employeeId}/attachment/${attachmentId}`,
      {
        headers: { Authorization: `Bearer ${peoplesessionToken}` },
        method: "DELETE",
      },
    );
    return { success: true };
  }

  // Add or update an employee (if employeeId is provided, update; else, add new)
  async saveEmployee(formData: FormData, employeeId?: string) {
    try {
      const peoplesessionToken = await getPeopleSessionToken();
      const apiFormData = new FormData();

      for (const [key, value] of formData.entries()) {
        let formattedKey;
        if (key === "attachments[]") {
          formattedKey = `employee[attachments][]`;
        } else if (key.startsWith("employee[user_roles_list]")) {
          formattedKey = key;
        } else {
          formattedKey = `employee[${key}]`;
        }
        apiFormData.append(formattedKey, value);
      }

      const url = employeeId
        ? `/api/employees/${employeeId}`
        : "/api/employees";
      const method = employeeId ? "PUT" : "POST";

      const response = await this.requestWithFormData<TEmployee>(url, {
        headers: { Authorization: `Bearer ${peoplesessionToken}` },
        method,
        body: apiFormData,
      });

      return response;
    } catch (error) {
      throw error;
    }
  }

  // Get employee leaves statistics
  async useEmployeeLeavesStats(employeeId: string) {
    const peoplesessionToken = await getPeopleSessionToken();

    const response = await this.request(
      `/api/employees/${employeeId}/leaves-stats`,
      {
        headers: { Authorization: `Bearer ${peoplesessionToken}` },
        method: "GET",
      },
    );

    return response;
  }

  async getGlobalLeavesData(
    page: number = 1,
    limit: number = 10,
    include: string,
    search: string = "",
    sort: string = "-updated_at",
    filters: string = "",
  ) {
    const peoplesessionToken = await getPeopleSessionToken();

    let url = `api/leaves?page[number]=${page}&page[size]=${limit}&sort=${sort}`;

    if (search) {
      url += `&filter[search]=${encodeURIComponent(search)}`;
    }

    if (include) {
      url += `&include=${include}`;
    }

    if (filters) {
      url += `&${filters}`;
    }

    const response = await this.request<EmployeeLeavesResponse>(url, {
      headers: { Authorization: `Bearer ${peoplesessionToken}` },
      method: "GET",
    });

    return response;
  }

  async getEmployeeLeaveDetails(
    employeeId: string,
    page: number = 1,
    limit: number = 5,
  ) {
    const peoplesessionToken = await getPeopleSessionToken();

    const response = await this.request<EmployeeLeavesResponse>(
      `api/employees/${employeeId}/leaves?page[number]=${page}&page[size]=${limit}`,
      {
        headers: { Authorization: `Bearer ${peoplesessionToken}` },
        method: "GET",
      },
    );

    return response;
  }

  async getSalaryPackageHistory(
    employeeId: string,
    page: number = 1,
    limit: number = 5,
    sortedBy: string = "-id",
  ) {
    const peoplesessionToken = await getPeopleSessionToken();

    // Build the URL following the finance endpoint pattern
    const url = `api/finance/salary_packages?sort=${sortedBy}&page[number]=${page}&page[size]=${limit}&filter[employee_id_eq]=${employeeId}`;

    const response = await this.request<EmployeeLeavesResponse>(url, {
      headers: { Authorization: `Bearer ${peoplesessionToken}` },
      method: "GET",
    });

    return response;
  }

  // Update leave dates
  async updateLeaveDates(
    employeeId: string,
    leaveId: string,
    startDate: string,
    endDate: string,
    leaveDuration?: string,
  ) {
    try {
      const peoplesessionToken = await getPeopleSessionToken();

      const formData = new FormData();
      formData.append("leave[start_date]", startDate);
      formData.append("leave[end_date]", endDate);

      if (leaveDuration) {
        formData.append("leave[leave_duration]", leaveDuration);
      }

      await this.requestWithFormData(
        `/api/employees/${employeeId}/leaves/${leaveId}`,
        {
          headers: { Authorization: `Bearer ${peoplesessionToken}` },
          method: "PUT",
          body: formData,
        },
      );

      return {
        data: {
          id: leaveId,
          startDate,
          endDate,
          updatedAt: new Date().toISOString(),
        },
      };
    } catch (error) {
      throw error;
    }
  }


  async createAttendance(employeeId: string, formData: FormData) {
  try {
    const peoplesessionToken = await getPeopleSessionToken();

    
    const apiFormData = new FormData();

    for (const [key, value] of formData.entries()) {
      
      if (key === "documents[]") {
        apiFormData.append(`attendance_event[documents][]`, value);
      } else if (key === "note") {
        apiFormData.append(`attendance_event[note]`, value);
      }
       else {
        apiFormData.append(`attendance_event[${key}]`, value);
      }
    }

    // نرسل الـ request مستخدمين الـ apiFormData الجديد
    const response = await this.requestWithFormData(
      `/api/attendance/events`, 
      {
        headers: { Authorization: `Bearer ${peoplesessionToken}` },
        method: "POST",
        body: apiFormData,
      },
    );

    return response;
  } catch (error) {
    throw error;
  }
}






  async getPeopleAttendance(params: string) {
    const peoplesessionToken = await getPeopleSessionToken();
    let headerToken: string | null = null;
    // If not found in cookies, try to get from headers (for API routes)
    if (!peoplesessionToken) {
      const reqHeaders = headers();
      headerToken =
        (await reqHeaders).get("authorization")?.replace(/^Token\s+/i, "") ||
        null;
    }

    // Build the URL with or without employee filter
    const url = params
      ? `api/attendance/events?sort=-id&${params}`
      : `api/attendance/events?sort=-id`;

    const response = await this.request<AttendanceEventResponse>(url, {
      headers: {
        Authorization: peoplesessionToken
          ? `Bearer ${peoplesessionToken}`
          : `${headerToken}`,
      },
      method: "GET",
    });

    return response;
  }

  async getAttendanceExemptions() {
    const peoplesessionToken = await getPeopleSessionToken();
    let headerToken: string | null = null;
    // If not found in cookies, try to get from headers (for API routes)
    if (!peoplesessionToken) {
      const reqHeaders = headers();
      headerToken =
        (await reqHeaders).get("authorization")?.replace(/^Token\s+/i, "") ||
        null;
    }

    // Build the URL with or without employee filter
    const url = `api/attendance/exemptions`;

    const response = await this.request<THolidaysResponse>(url, {
      headers: {
        Authorization: peoplesessionToken
          ? `Bearer ${peoplesessionToken}`
          : `${headerToken}`,
      },
      method: "GET",
    });

    return response;
  }

  async createHoliday(formData: FormData) {
    try {
      const peoplesessionToken = await getPeopleSessionToken();

      const apiFormData = new FormData();

      for (const [key, value] of formData.entries()) {
        const formattedKey = `attendance_exemption[${key}]`;
        apiFormData.append(formattedKey, value);
      }

      const response = await this.requestWithFormData(
        `/api/attendance/exemptions`,
        {
          headers: { Authorization: `Bearer ${peoplesessionToken}` },
          method: "POST",
          body: apiFormData,
        },
      );

      return response;
    } catch (error) {
      throw error;
    }
  }

  async updateHoliday(holidayId: string, formData: FormData) {
    try {
      const peoplesessionToken = await getPeopleSessionToken();

      const apiFormData = new FormData();

      for (const [key, value] of formData.entries()) {
        const formattedKey = `attendance_exemption[${key}]`;
        apiFormData.append(formattedKey, value);
      }

      const response = await this.requestWithFormData(
        `/api/attendance/exemptions/${holidayId}`,
        {
          headers: { Authorization: `Bearer ${peoplesessionToken}` },
          method: "PUT",
          body: apiFormData,
        },
      );

      return response;
    } catch (error) {
      throw error;
    }
  }

  async deleteHoliday(holidayId: string): Promise<void> {
    const peoplesessionToken = await getPeopleSessionToken();

    await this.request(`/api/attendance/exemptions/${holidayId}`, {
      headers: { Authorization: `Bearer ${peoplesessionToken}` },
      method: "DELETE",
    });
  }

  async getSalaryCalculations(
    page: number = 1,
    limit: number = 5,
    sort: string = "-period_start_date",
    search: string = "",
    filters: string = "",
  ) {
    const peoplesessionToken = await getPeopleSessionToken();

    // Build the URL with modern filtering approach
    let url = `api/finance/salary_calculations?sort=${sort}&page[number]=${page}&page[size]=${limit}`;

    if (search) {
      url += `&search=${encodeURIComponent(search)}`;
    }

    if (filters) {
      url += `&${filters}`;
    }

    // Add include=employee if no specific employee filter is present
    if (!filters.includes("employee_id_eq")) {
      url += `&include=employee`;
    }

    const response = await this.request<EmployeeLeavesResponse>(url, {
      headers: { Authorization: `Bearer ${peoplesessionToken}` },
      method: "GET",
    });

    return response;
  }



  // Regenerate salary slip (server-side only)
  async regenerateSalarySlip(salaryCalculationId: string) {
    const peoplesessionToken = await getPeopleSessionToken();

    const response = await this.request(
      `api/finance/salary_calculations/${salaryCalculationId}/regenerate_slip`,
      {
        headers: { Authorization: `Bearer ${peoplesessionToken}` },
        method: "POST",
      },
    );
    return response;
  }

  // Get salary slip for preview or download
  async getSalarySlip(
    salaryCalculationId: string,
    action: "preview" | "download",
  ) {
    const peoplesessionToken = await getPeopleSessionToken();

    try {
      if (action === "preview") {
        // For preview, use the actual backend endpoint
        const response = await this.request(
          `api/finance/salary_calculations/${salaryCalculationId}/preview_slip`,
          {
            headers: { Authorization: `Bearer ${peoplesessionToken}` },
            method: "GET",
          },
        );
        return response;
      } else {
        // For download, use the actual backend endpoint
        const response = await fetch(
          `${this.baseURL}/api/finance/salary_calculations/${salaryCalculationId}/download_slip`,
          {
            headers: {
              Authorization: `Bearer ${peoplesessionToken}`,
              "Accept-Language": await getUserLanguage(),
            },
            method: "GET",
          },
        );

        return response;
      }
    } catch (error) {
      throw error;
    }
  }

  // Calculate salary for a specific period
  async calculateSalaryPeriod(period: string) {
    try {
      const peoplesessionToken = await getPeopleSessionToken();

      const response = await this.request(
        `api/finance/salary_calculations/calculate_period?period=${period}`,
        {
          headers: { Authorization: `Bearer ${peoplesessionToken}` },
          method: "POST",
        },
      );

      return response;
    } catch (error) {
      throw error;
    }
  }

  // Update salary status (approve/reject)
  async updateSalaryStatus(salaryId: string, status: string) {
    try {
      const peoplesessionToken = await getPeopleSessionToken();

      const response = await this.request(
        `api/finance/salary_calculations/${salaryId}`,
        {
          headers: {
            Authorization: `Bearer ${peoplesessionToken}`,
            "Content-Type": "application/json",
          },
          method: "PATCH",
          body: JSON.stringify({
            salary_calculation: { status }
          }),
        },
      );

      return response;
    } catch (error) {
      throw error;
    }
  }

  // Common method for handling approval and rejection
  async handleApprovalRequest(
    approvalRequestId: string,
    action: "approve" | "reject",
    comment: string = "",
  ) {
    try {
      const peoplesessionToken = await getPeopleSessionToken();

      const endpoint = `/api/approval_requests/${approvalRequestId}/${action}${
        comment ? `?comment=${encodeURIComponent(comment)}` : ""
      }`;

      const response = await this.request<ApprovalRequestResponse>(endpoint, {
        headers: { Authorization: `Bearer ${peoplesessionToken}` },
        method: "POST",
      });

      // The response contains the full approval request data
      // We'll return both the simplified status info and the full response
      // Note: response.data is an array, so we take the first item
      return {
        data: {
          id: approvalRequestId,
          status: action === "approve" ? "approved" : "rejected",
          updatedAt: new Date().toISOString(),
          approvalRequest: response.data[0],
        },
      };
    } catch (error) {
      throw error;
    }
  }

  // Convenience methods that use the common handler
  async approveLeaveRequest(approvalRequestId: string, comment: string = "") {
    return this.handleApprovalRequest(approvalRequestId, "approve", comment);
  }

  // This is used by the new API directly
  async _rejectLeaveRequest(approvalRequestId: string, comment: string = "") {
    return this.handleApprovalRequest(approvalRequestId, "reject", comment);
  }

  async withdrawLeaveRequest(employeeId: string, leaveId: string) {
    try {
      const peoplesessionToken = await getPeopleSessionToken();

      const endpoint = `/api/employees/${employeeId}/leaves/${leaveId}/withdraw`;

      await this.request(endpoint, {
        headers: { Authorization: `Bearer ${peoplesessionToken}` },
        method: "POST",
      });

      return {
        data: {
          id: leaveId,
          status: "withdrawn",
          updatedAt: new Date().toISOString(),
        },
      };
    } catch (error) {
      throw error;
    }
  }

  // Legacy methods for backward compatibility
  async updateLeaveStatus(
    employeeId: string,
    leaveId: string,
    status: "withdrawn" | "approved" | "rejected",
    approvalRequestId?: string,
    comment: string = "",
  ) {
    try {
      if (status === "withdrawn") {
        return this.withdrawLeaveRequest(employeeId, leaveId);
      } else if (status === "approved" && approvalRequestId) {
        return this.approveLeaveRequest(approvalRequestId, comment);
      } else if (status === "rejected" && approvalRequestId) {
        return this._rejectLeaveRequest(approvalRequestId, comment);
      } else {
        throw new Error("Invalid status or missing approval request ID");
      }
    } catch (error) {
      throw error;
    }
  }

  async getLeaveAttachments(
    leaveId: string,
    page: number = 1,
    limit: number = 20,
  ) {
    try {
      const peoplesessionToken = await getPeopleSessionToken();

      const endpoint = `/api/leaves/${leaveId}/documents?page[number]=${page}&page[size]=${limit}`;

      const response = await this.request<TEmployeeAttachmentsResponse>(
        endpoint,
        {
          headers: { Authorization: `Bearer ${peoplesessionToken}` },
          method: "GET",
        },
      );

      return response;
    } catch (error) {
      throw error;
    }
  }

  // Create a new leave
  async createLeave(employeeId: string, formData: FormData) {
    try {
      const peoplesessionToken = await getPeopleSessionToken();

      const apiFormData = new FormData();

      for (const [key, value] of formData.entries()) {
        let formattedKey;

        if (key === "documents[]") {
          formattedKey = `leave[documents][]`;
        } else {
          formattedKey = `leave[${key}]`;
        }

        // Only append the value once
        apiFormData.append(formattedKey, value);
      }

      const response = await this.requestWithFormData(
        `/api/employees/${employeeId}/leaves`,
        {
          headers: { Authorization: `Bearer ${peoplesessionToken}` },
          method: "POST",
          body: apiFormData,
        },
      );

      return response;
    } catch (error) {
      throw error;
    }
  }

  async saveSalaryPackage(formData: FormData) {
    try {
      const peoplesessionToken = await getPeopleSessionToken();
      const apiFormData = new FormData();

      // Check if this is an update (has ID)
      const packageId = formData.get("id");
      const isUpdate = packageId && packageId !== "";

      for (const [key, value] of formData.entries()) {
        if (key !== "id") {
          const formattedKey = `salary_package[${key}]`;
          apiFormData.append(formattedKey, value);
        }
      }

      // Use different endpoints for create vs update
      const url = isUpdate
        ? `/api/finance/salary_packages/${packageId}`
        : `/api/finance/salary_packages`;

      const method = isUpdate ? "PUT" : "POST";

      const response = await this.requestWithFormData<TSalaryPackageResponse>(
        url,
        {
          headers: { Authorization: `Bearer ${peoplesessionToken}` },
          method,
          body: apiFormData,
        },
      );

      return response;
    } catch (error) {
      throw error;
    }
  }

  // Submit salary package for approval (draft -> pending)
  async submitSalaryPackage(packageId: string) {
    try {
      const peoplesessionToken = await getPeopleSessionToken();

      const response = await this.request<TSalaryPackageResponse>(
        `/api/finance/salary_packages/${packageId}/submit`,
        {
          headers: { Authorization: `Bearer ${peoplesessionToken}` },
          method: "PATCH",
        },
      );

      return response;
    } catch (error) {
      throw error;
    }
  }

  // Cancel salary package (draft -> cancelled)
  async cancelSalaryPackage(packageId: string) {
    try {
      const peoplesessionToken = await getPeopleSessionToken();

      const response = await this.request<TSalaryPackageResponse>(
        `/api/finance/salary_packages/${packageId}/cancel`,
        {
          headers: { Authorization: `Bearer ${peoplesessionToken}` },
          method: "PATCH",
        },
      );

      return response;
    } catch (error) {
      throw error;
    }
  }

  // Delete salary package
  async deleteSalaryPackage(packageId: string): Promise<void> {
    const peoplesessionToken = await getPeopleSessionToken();

    await this.request(`/api/finance/salary_packages/${packageId}`, {
      headers: { Authorization: `Bearer ${peoplesessionToken}` },
      method: "DELETE",
    });
  }

  // Delete a user role from an employee
  async deleteUserRole(employeeId: string, userRoleId: string) {
    try {
      const peoplesessionToken = await getPeopleSessionToken();

      // Use a direct fetch call to handle DELETE response properly
      const response = await fetch(
        `${this.baseURL}/api/employees/${employeeId}/user_roles/${userRoleId}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${peoplesessionToken}`,
            "Accept-Language": await getUserLanguage(),
          },
        },
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const error = new Error(
          errorData.error ||
            errorData.errors?.[0]?.detail ||
            response.statusText,
        ) as ApiError;
        error.status = response.status;
        throw error;
      }

      // DELETE operations typically return 204 No Content, which is success
      return {
        success: true,
        data: {
          id: userRoleId,
          employeeId,
          deletedAt: new Date().toISOString(),
        },
      };
    } catch (error) {
      throw error;
    }
  }

  // Add a user role to an employee
  async addUserRole(employeeId: string, roleId: string, projectId: string) {
    try {
      const peoplesessionToken = await getPeopleSessionToken();

      const formData = new FormData();
      formData.append("user_role[role_id]", roleId);
      formData.append("user_role[project_id]", projectId);

      const response = await this.requestWithFormData(
        `/api/employees/${employeeId}/user_roles`,
        {
          headers: { Authorization: `Bearer ${peoplesessionToken}` },
          method: "POST",
          body: formData,
        },
      );

      return {
        success: true,
        data: response,
      };
    } catch (error) {
      throw error;
    }
  }

  async getMe(include = "user_roles.role"): Promise<TEmployeeResponse> {
    try {
      const peoplesessionToken = await getPeopleSessionToken();
      const url = `/api/me?include=${include}`;

      const res = await this.request<TEmployeeResponse>(url, {
        headers: { Authorization: `Bearer ${peoplesessionToken}` },
        method: "GET",
      });

      return res;
    } catch (err) {
      throw err;
    }
  }

  async getDevices(
    page: number = 1,
    limit: number = 5,
    sort: string = "created_at",
    search: string = "",
    filters: string = "",
  ): Promise<TDeviceResponse> {
    const peoplesessionToken = await getPeopleSessionToken();

    let url = `/api/attendance/devices?sort=${sort}&page[number]=${page}&page[size]=${limit}`;
    if (search) {
      url += `&filter[search]=${encodeURIComponent(search)}`;
    }
    if (filters) {
      url += `&${filters}`;
    }

    const response = await this.request<TDeviceResponse>(url, {
      headers: { Authorization: `Bearer ${peoplesessionToken}` },
      method: "GET",
    });

    return response;
  }

  async getAttendanceEvents(
    page: number = 1,
    limit: number = 5,
    sort: string = "-timestamp",
    search: string = "",
    filters: string = "",
  ): Promise<AttendanceEventResponse> {
    const peoplesessionToken = await getPeopleSessionToken();

    let url = `/api/attendance/events?include=employee&sort=${sort}&page[number]=${page}&page[size]=${limit}`;
    if (search) {
      url += `&filter[search]=${encodeURIComponent(search)}`;
    }
    if (filters) {
      url += `&${filters}`;
    }

    const response = await this.request<AttendanceEventResponse>(url, {
      headers: { Authorization: `Bearer ${peoplesessionToken}` },
      method: "GET",
    });

    return response;
  }

  // Get a single device by ID
  async getDevice(deviceId: string): Promise<TDevice> {
    const peoplesessionToken = await getPeopleSessionToken();

    const response = await this.request<TDevice>(
      `/api/attendance/devices/${deviceId}`,
      {
        headers: { Authorization: `Bearer ${peoplesessionToken}` },
        method: "GET",
      },
    );

    return response;
  }

  // Update an existing device
  async updateDevice(deviceId: string, formData: FormData): Promise<TDevice> {
    const peoplesessionToken = await getPeopleSessionToken();
    const apiFormData = new FormData();

    for (const [key, value] of formData.entries()) {
      const formattedKey = `attendance_device[${key}]`;
      apiFormData.append(formattedKey, value);
    }

    const response = await this.requestWithFormData<TDevice>(
      `/api/attendance/devices/${deviceId}`,
      {
        headers: { Authorization: `Bearer ${peoplesessionToken}` },
        method: "PATCH",
        body: apiFormData,
      },
    );

    return response;
  }

  // Add a new device
  async addNewDevice(formData: FormData): Promise<TDevice> {
    const peoplesessionToken = await getPeopleSessionToken();
    const apiFormData = new FormData();

    for (const [key, value] of formData.entries()) {
      const formattedKey = `attendance_device[${key}]`;
      apiFormData.append(formattedKey, value);
    }

    const response = await this.requestWithFormData<TDevice>(
      "/api/attendance/devices",
      {
        headers: { Authorization: `Bearer ${peoplesessionToken}` },
        method: "POST",
        body: apiFormData,
      },
    );

    return response;
  }

  // Delete a device
  async deleteDevice(deviceId: string): Promise<void> {
    const peoplesessionToken = await getPeopleSessionToken();

    await this.request(`/api/attendance/devices/${deviceId}`, {
      headers: { Authorization: `Bearer ${peoplesessionToken}` },
      method: "DELETE",
    });
  }

  // Get device users with pagination
  async getDeviceUsers(
    deviceId: string,
    page: number = 1,
    limit: number = 25,
    filters: string = "",
  ): Promise<TDeviceUsersResponse> {
    const peoplesessionToken = await getPeopleSessionToken();

    let url = `/api/attendance/devices/${deviceId}/users?page[number]=${page}&page[size]=${limit}&include=employee`;
    if (filters) {
      url += `&${filters}`;
    }

    try {
      const data = await this.request<TDeviceUsersResponse>(url, {
        headers: { Authorization: `Bearer ${peoplesessionToken}` },
        method: "GET",
      });

      return data;
    } catch (error) {
      throw error;
    }
  }

  // Get single device user
  async getDeviceUser(
    deviceId: string,
    userId: string,
  ): Promise<TDeviceUserResponse> {
    const peoplesessionToken = await getPeopleSessionToken();

    const response = await this.request<TDeviceUserResponse>(
      `/api/attendance/devices/${deviceId}/users/${userId}`,
      {
        headers: { Authorization: `Bearer ${peoplesessionToken}` },
        method: "GET",
      },
    );

    return response;
  }

  // Create device user
  async createDeviceUser(
    deviceId: string,
    formData: FormData,
  ): Promise<TDeviceUserResponse> {
    const peoplesessionToken = await getPeopleSessionToken();
    const apiFormData = new FormData();

    // Add device_id to form data
    apiFormData.append("device_id", deviceId);

    // Transform form data to match API format
    for (const [key, value] of formData.entries()) {
      const formattedKey = `user[${key}]`;
      apiFormData.append(formattedKey, value);
    }

    const response = await this.requestWithFormData<TDeviceUserResponse>(
      `/api/attendance/devices/${deviceId}/users`,
      {
        headers: { Authorization: `Bearer ${peoplesessionToken}` },
        method: "POST",
        body: apiFormData,
      },
    );

    return response;
  }

  // Delete device user
  async deleteDeviceUser(deviceId: string, userId: string): Promise<void> {
    const peoplesessionToken = await getPeopleSessionToken();

    await this.request(`/api/attendance/devices/${deviceId}/users/${userId}`, {
      headers: { Authorization: `Bearer ${peoplesessionToken}` },
      method: "DELETE",
    });
  }

  // Create new device user mapping
  async createDeviceUserMapping(
    deviceId: string,
    systemCode: string,
    deviceCode: string,
  ): Promise<TDeviceUserMappingResponse> {
    const peoplesessionToken = await getPeopleSessionToken();
    // Create form data for new mapping
    const formData = new FormData();
    formData.append("employee_device_mapping[employee_id]", `${systemCode}`);
    formData.append("employee_device_mapping[device_user_id]", `${deviceCode}`);
    formData.append(
      "employee_device_mapping[notes]",
      `Created via fix code dialog`,
    );

    const result = await this.requestWithFormData<TDeviceUserMappingResponse>(
      `/api/attendance/devices/${deviceId}/mappings`,
      {
        headers: { Authorization: `Bearer ${peoplesessionToken}` },
        method: "POST",
        body: formData,
      },
    );

    return result;
  }

  // Get available commands for a device
  async getAvailableDeviceCommands(
    deviceId: string,
  ): Promise<TAvailableCommandsResponse> {
    const peoplesessionToken = await getPeopleSessionToken();

    const result = await this.request<TAvailableCommandsResponse>(
      `/api/attendance/devices/${deviceId}/commands`,
      {
        headers: {
          Authorization: `Bearer ${peoplesessionToken}`,
          "Content-Type": "application/json",
        },
        method: "GET",
      },
    );

    return result;
  }

  // Execute device command
  async executeDeviceCommand(
    deviceId: string,
    queryParams: URLSearchParams,
  ): Promise<TCommandExecutionResponse> {
    const peoplesessionToken = await getPeopleSessionToken();
    const url = `/api/attendance/devices/${deviceId}/commands/execute?${queryParams.toString()}`;

    const result = await this.request<TCommandExecutionResponse>(url, {
      headers: {
        Authorization: `Bearer ${peoplesessionToken}`,
        "Content-Type": "application/json",
      },
      method: "POST",
    });

    return result;
  }

  // Update device user mapping (fix codes)
  async updateDeviceUserCodes(
    deviceId: string,
    mappingId: string, // This should be the mapping ID, not user ID
    systemCode: string,
    deviceCode: string,
  ): Promise<TDeviceUserMappingResponse> {
    const peoplesessionToken = await getPeopleSessionToken();

    // Create form data exactly as shown in the curl command with quoted values
    const formData = new FormData();
    formData.append("employee_device_mapping[employee_id]", `${systemCode}`);
    formData.append("employee_device_mapping[device_user_id]", `${deviceCode}`);
    formData.append(
      "employee_device_mapping[notes]",
      `Updated via fix code dialog`,
    );

    const result = await this.requestWithFormData<TDeviceUserMappingResponse>(
      `/api/attendance/devices/${deviceId}/mappings/${mappingId}`,
      {
        headers: { Authorization: `Bearer ${peoplesessionToken}` },
        method: "PATCH",
        body: formData,
      },
    );

    return result;
  }

  async exportEmployees(
    format: "csv" | "pdf" | "xlsx",
    filters?: {
      search?: string;
      sort?: string;
      page?: number;
      limit?: number;
      [key: string]: string | number | boolean | undefined;
    },
  ): Promise<void> {
    const peopleSessionToken = await getPeopleSessionToken();

    // Build query string from filters
    const queryParams = new URLSearchParams();

    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          if (key === "search") {
            queryParams.append("filter[search]", encodeURIComponent(value));
          } else if (key === "page") {
            queryParams.append("page[number]", value.toString());
          } else if (key === "limit") {
            queryParams.append("page[size]", value.toString());
          } else if (key === "sort") {
            queryParams.append("sort", value.toString());
          } else {
            queryParams.append(key, value.toString());
          }
        }
      });
    }

    const url = `/api/employees.${format}${queryParams.toString() ? `?${queryParams.toString()}` : ""}`;

    try {
      const response = await fetch(`${this.baseURL}${url}`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${peopleSessionToken}`,
          Accept: this.getAcceptHeader(format),
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(
          `Export failed: ${response.status} ${response.statusText}. ${errorText}`,
        );
      }

      // Download file using file-saver
      const blob = await response.blob();
      const { saveAs } = await import("file-saver");
      const filename = `employees-${new Date().toISOString().split("T")[0]}.${format}`;
      saveAs(blob, filename);
    } catch (error) {
      console.error("Export failed:", error);
      throw error;
    }
  }

  private getAcceptHeader(format: "csv" | "pdf" | "xlsx"): string {
    switch (format) {
      case "csv":
        return "text/csv";
      case "pdf":
        return "application/pdf";
      case "xlsx":
        return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      default:
        return "application/json";
    }
  }
}

// Create a singleton instance
export const peopleService = new PeopleService();

export const getPeopleSessionToken = async (): Promise<string | null> => {
  const cookieStore = await cookies();
  return cookieStore.get("people_session_token")?.value || null;
};
