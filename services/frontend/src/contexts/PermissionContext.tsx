"use client";

import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  useCallback,
} from "react";
import { Permission } from "@/types/auth";
import { useUserPermission } from "@/app/[locale]/_modules/people/hooks/user/useUserPermission";
import {
  saveDataToSessionStorage,
  getDataFromSessionStorage,
} from "@/lib/local-storage";
import { useSystem } from "./system-provider";

interface PermissionContextType {
  hasPermission: (permission: string) => boolean;
  permissions: Permission[];
  isLoading: boolean;
  error: Error | null;
  refetch: () => void;
  clearPermissions: () => void;
}

const PermissionContext = createContext<PermissionContextType | null>(null);

export const PermissionProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const { currSystem } = useSystem();
  const {
    permission: apiPermissions,
    isLoading,
    error,
    mutate,
  } = useUserPermission(currSystem);

  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [isPermissionsLoaded, setIsPermissionsLoaded] = useState(false);
  // Create system-specific storage key
  const STORAGE_KEY = `user_permissions_${currSystem}`;

  // Clear permissions when system changes
  useEffect(() => {
    setPermissions([]);
    setIsPermissionsLoaded(false);
    // Clear all system-specific permission storage when system changes
    if (typeof window !== "undefined") {
      const systems = ["people", "procure", "cm", "core"];
      systems.forEach((system) => {
        if (system !== currSystem) {
          sessionStorage.removeItem(`user_permissions_${system}`);
        }
      });
    }
  }, [currSystem]);

  // Load permissions from session storage for current system
  useEffect(() => {
    console.log("🔍 PERMISSION CONTEXT - Loading from storage:");
    console.log("  currSystem:", currSystem);
    console.log("  STORAGE_KEY:", STORAGE_KEY);

    if (currSystem && currSystem !== "core") {
      const storedPermissions =
        getDataFromSessionStorage<Permission[]>(STORAGE_KEY);
      console.log("  storedPermissions:", storedPermissions);

      if (storedPermissions && storedPermissions.length > 0) {
        console.log("  ✅ Loading permissions from storage:", storedPermissions.length, "permissions");
        setPermissions(storedPermissions);
        setIsPermissionsLoaded(true);
      } else {
        console.log("  ❌ No stored permissions found");
      }
    } else {
      console.log("  ⚠️ Skipping storage load - currSystem is core or undefined");
    }
  }, [currSystem, STORAGE_KEY]);

  // Clear permissions from both state and session storage
  const clearPermissions = useCallback(() => {
    setPermissions([]);
    setIsPermissionsLoaded(false);
    if (typeof window !== "undefined") {
      sessionStorage.removeItem(STORAGE_KEY);
    }
  }, [STORAGE_KEY]);

  // Update permissions when API data changes
  useEffect(() => {
    console.log("🔍 PERMISSION CONTEXT - API permissions update:");
    console.log("  apiPermissions:", apiPermissions);
    console.log("  apiPermissions length:", apiPermissions?.length || 0);
    console.log("  isLoading:", isLoading);
    console.log("  error:", error);

    if (apiPermissions && apiPermissions.length > 0) {
      console.log("  ✅ Saving API permissions to storage and state");
      console.log("  salaryPermissions from API:", apiPermissions.filter(p => p.id.includes("salary")).map(p => p.id));
      saveDataToSessionStorage(STORAGE_KEY, apiPermissions);
      setPermissions(apiPermissions);
      setIsPermissionsLoaded(true);
    } else {
      console.log("  ❌ No API permissions to save");
    }
  }, [apiPermissions, STORAGE_KEY]);

  const hasPermission = useCallback(
    (perm: string) => {
      const hasIt = permissions.some((p) => p.id === perm);

      // 🐛 DEBUG LOGGING - Only log salary-related permissions
      if (perm.includes("salary_calculation")) {
        console.log("🔍 PERMISSION CHECK:");
        console.log("  permission:", perm);
        console.log("  hasPermission:", hasIt);
        console.log("  allPermissions:", permissions.map(p => p.id));
        console.log("  salaryPermissions:", permissions.filter(p => p.id.includes("salary")).map(p => p.id));
      }

      return hasIt;
    },
    [permissions],
  );

  const shouldRenderChildren = isPermissionsLoaded || error || (currSystem === "core") || (!isLoading && !apiPermissions);

  return (
    <PermissionContext.Provider
      value={{
        permissions,
        hasPermission,
        isLoading,
        error,
        refetch: mutate,
        clearPermissions,
      }}
    >
      {shouldRenderChildren ? children : null}
    </PermissionContext.Provider>
  );
};

export const usePermission = (): PermissionContextType => {
  const ctx = useContext(PermissionContext);
  if (!ctx)
    throw new Error("usePermission must be used within PermissionProvider");
  return ctx;
};
