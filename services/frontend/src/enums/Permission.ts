export enum PermissionEnum {
  MANAGE_EMPLOYEE = "manage:employee",
  READ_EMPLOYEE = "read:employee",
  CREATE_EMPLOYEE = "create:employee",
  UPDATE_EMPLOYEE = "update:employee",
  DESTROY_EMPLOYEE = "destroy:employee",
  READ_OWN_EMPLOYEE = "read_own:employee",
  UPDATE_OWN_EMPLOYEE = "update_own:employee",
  MANAGE_PAYROLL = "manage:payroll",
  READ_PAYROLL = "read:payroll",
  CREATE_PAYROLL = "create:payroll",
  UPDATE_PAYROLL = "update:payroll",
  DESTROY_PAYROLL = "destroy:payroll",
  MANAGE_LEAVE = "manage:leave",
  READ_LEAVE = "read:leave",
  CREATE_LEAVE = "create:leave",
  UPDATE_LEAVE = "update:leave",
  SUBMIT_LEAVE = "submit:leave",
  WITHDRAW_LEAVE = "withdraw:leave",
  APPROVE_LEAVE = "approve:leave",
  REJECT_LEAVE = "reject:leave",
  READ_OWN_LEAVE = "read_own:leave",
  MANAGE_OWN_LEAVE = "manage_own:leave",
  MANAGE_ATTENDANCE_EVENT = "manage:attendance_event",
  READ_ATTENDANCE_EVENT = "read:attendance_event",
  CREATE_ATTENDANCE_EVENT = "create:attendance_event",
  UPDATE_ATTENDANCE_EVENT = "update:attendance_event",
  DESTROY_ATTENDANCE_EVENT = "destroy:attendance_event",
  RECORD_ATTENDANCE_EVENT = "record:attendance_event",
  READ_OWN_ATTENDANCE_EVENT = "read_own:attendance_event",
  MANAGE_SETTING = "manage:setting",
  READ_SETTING = "read:setting",
  CREATE_SETTING = "create:setting",
  UPDATE_SETTING = "update:setting",
  GENERATE_HR_REPORT = "generate:hr_report",
  EXPORT_HR_REPORT = "export:hr_report",
  READ_APPROVAL_REQUEST = "read:approval_request",
  READ_OWN_APPROVAL_REQUEST = "read_own:approval_request",
  APPROVE_APPROVAL_REQUEST = "approve:approval_request",
  REJECT_APPROVAL_REQUEST = "reject:approval_request",
  CANCEL_APPROVAL_REQUEST = "cancel:approval_request",
  CANCEL_OWN_APPROVAL_REQUEST = "cancel_own:approval_request",
  MANAGE_SALARY_PACKAGE = "manage:salary_package",
  READ_SALARY_PACKAGE = "read:salary_package",
  READ_OWN_SALARY_PACKAGE = "read_own:salary_package",
  UPDATE_OWN_SALARY_PACKAGE = "update_own:salary_package",
  MANAGE_OTHERS_SALARY_PACKAGE = "manage_others:salary_package",
  CREATE_SALARY_PACKAGE = "create:salary_package",
  CREATE_OTHERS_SALARY_PACKAGE = "create_others:salary_package",
  APPROVE_SALARY_PACKAGE = "approve:salary_package",
  READ_SALARY_CALCULATION = "read:salary_calculation",
  READ_OWN_SALARY_CALCULATION = "read_own:salary_calculation",
  CALCULATE_SALARY_CALCULATION = "calculate:salary_calculation",
  UPDATE_SALARY_CALCULATION = "update:salary_calculation",
  SUBMIT_SALARY_CALCULATION = "submit:salary_calculation",
  APPROVE_SALARY_CALCULATION = "approve:salary_calculation",
  MANAGE_TAX_CONFIG = "manage:tax_config",
  MANAGE_SOCIAL_SECURITY_CONFIG = "manage:social_security_config",
  // Project permissions
  MANAGE_PROJECT = "manage:project",
  READ_PROJECT = "read:project",
  CREATE_PROJECT = "create:project",
  UPDATE_PROJECT = "update:project",
  DESTROY_PROJECT = "destroy:project",
  NOAccess = "testing", //TODO: remove after test
}
