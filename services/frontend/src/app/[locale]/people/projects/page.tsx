"use client";

import React, { Suspense } from "react";
import ProjectsHeader from "../../_modules/people/_components/projects/header/projects-header";
import ProjectsTable from "../../_modules/people/_components/projects/table";
import { useTranslations } from "next-intl";
import { useSearchParams } from "next/navigation";
import { TFunction } from "@/types";

const Projects = () => {
  const searchParams = useSearchParams();
  const t = useTranslations("projects.page") as TFunction;
  
  // Convert searchParams to the expected format
  const params: { [key: string]: string | string[] | undefined } = {};
  searchParams.forEach((value, key) => {
    params[key] = value;
  });

  return (
    <>
      <ProjectsHeader title={t("title")} />
      <Suspense fallback={<div>{"Loading..."}</div>}>
        <ProjectsTable showPagination={true} searchParams={params} />
      </Suspense>
    </>
  );
};

export default Projects;
