"use client";

import React, { useState } from "react";
import { useTranslations } from "next-intl";
import { TFunction } from "@/types";
import HolidayCalendar from "./holiday-calendar";
import HolidayForm from "./holiday-form";
import HolidayDetailsModal from "./holiday-details-modal";
import ConfirmDeleteDialog from "@/components/confirm-delete-dialog";
import dynamic from "next/dynamic";
import Loader from "@/components/loader";
import { DialogDescription, DialogTitle } from "@/components/ui/dialog";
import { useAttendanceExemptions } from "@/app/[locale]/_modules/people/hooks/attendance/useAttendanceExemptions";
import { useHolidayMutations } from "@/app/[locale]/_modules/settings/hooks/useHolidayMutations";
import { THoliday } from "@/types/settings/holidays";

const ResponsiveDialog = dynamic(
  () => import("@/components/responsive-dialog"),
  {
    ssr: false,
    loading: () => <Loader overlayColor="#000" overlayOpacity={0.6} />,
  },
);

const Holidays = () => {
  const t = useTranslations() as TFunction;
  const { holidays, mutate: mutateHolidaysList } = useAttendanceExemptions();
  const [showAddForm, setShowAddForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedHoliday, setSelectedHoliday] = useState<THoliday | null>(null);

  const { deleteHoliday, isDeleting } = useHolidayMutations({
    onSuccess: () => {
      mutateHolidaysList();
      setIsDeleteDialogOpen(false);
      setIsDetailsOpen(false);
    },
  });

  const handleAddHoliday = () => {
    setShowAddForm(true);
  };

  const handleHolidayClick = (holiday: THoliday) => {
    setSelectedHoliday(holiday);
    setIsDetailsOpen(true);
  };

  const handleFormSuccess = () => {
    setShowAddForm(false);
    setShowEditForm(false);
  };

  const handleFormCancel = () => {
    setShowAddForm(false);
    setShowEditForm(false);
  };

  const handleEditHoliday = (holiday: THoliday) => {
    setSelectedHoliday(holiday);
    setIsDetailsOpen(false);
    setShowEditForm(true);
  };

  const handleDeleteHoliday = (holiday: THoliday) => {
    setSelectedHoliday(holiday);
    setIsDetailsOpen(false);
    setIsDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (selectedHoliday) {
      await deleteHoliday(selectedHoliday.id.toString());
    }
  };

  return (
    <div className={`space-y-6`}>
      <HolidayCalendar
        holidays={holidays ?? []}
        onAddHoliday={handleAddHoliday}
        onHolidayClick={handleHolidayClick}
      />

      {/* Add Holiday Form Dialog */}
      <ResponsiveDialog
        open={showAddForm}
        onOpenChange={setShowAddForm}
        header={
          <div className="p-6 border border-b">
            <DialogTitle className="text-lg font-semibold">
              {t("settings.holidays.form.add-title")}
            </DialogTitle>
            <DialogDescription className="text-sm text-muted-foreground sr-only">
              {t("settings.holidays.form.add-description")}
            </DialogDescription>
          </div>
        }
      >
        <HolidayForm
          onSuccess={handleFormSuccess}
          onCancel={handleFormCancel}
          mode="add"
        />
      </ResponsiveDialog>

      {/* Edit Holiday Form Dialog */}
      <ResponsiveDialog
        open={showEditForm}
        onOpenChange={setShowEditForm}
        header={
          <div className="p-6 border border-b">
            <DialogTitle className="text-lg font-semibold">
              {t("settings.holidays.form.edit-title")}
            </DialogTitle>
            <DialogDescription className="text-sm text-muted-foreground sr-only">
              {t("settings.holidays.form.edit-description")}
            </DialogDescription>
          </div>
        }
      >
        <HolidayForm
          onSuccess={handleFormSuccess}
          onCancel={handleFormCancel}
          holiday={selectedHoliday}
          mode="edit"
        />
      </ResponsiveDialog>

      {/* Holiday Details Modal */}
      <HolidayDetailsModal
        holiday={selectedHoliday}
        isOpen={isDetailsOpen}
        onClose={() => setIsDetailsOpen(false)}
        onEdit={handleEditHoliday}
        onDelete={handleDeleteHoliday}
      />

      {/* Delete Holiday Dialog */}
      {selectedHoliday && (
        <ConfirmDeleteDialog
          isOpen={isDeleteDialogOpen}
          onClose={() => setIsDeleteDialogOpen(false)}
          onConfirmDelete={handleConfirmDelete}
          isDeleting={isDeleting}
          itemName={selectedHoliday.attributes.name}
          titleKey="settings.holidays.delete-dialog.title"
          descriptionKey="common.delete-dialog.description"
        />
      )}
    </div>
  );
};

export default Holidays;
