import useS<PERSON> from "swr";

import { fetcher } from "@/services/fetcher";
import { AttendanceEventResponse } from "../../type/employee-leaves";
import { useSearchParams } from "next/navigation";
import { useFilterParams } from "@/hooks/filters/useFilterParams";
import { useApiUrl } from "@/hooks/useApiUrl";

export const useAttendanceEvents = (
  page: number,
  limit: number,
  sortBy: string = "-timestamp",
  employeeId?: string,
  tableId: string = "attendance-requests",
) => {
  const searchParams = useSearchParams();
  const { filters } = useFilterParams(tableId);
  const searchQuery = searchParams.get("search") || "";

  // Add employee filter if provided
  const attendanceFilters = employeeId
    ? { ...filters, employee_id_eq: employeeId }
    : filters;

  const apiUrl = useApiUrl({
    baseUrl: "/api/attendance/events",
    page: Number(page),
    limit: Number(limit),
    sort: String(sortBy),
    search: searchQuery || undefined,
    filters: attendanceFilters,
    tableId: tableId,
  });

  const { data, error, isLoading, mutate } = useSWR<AttendanceEventResponse>(
    apiUrl,
    fetcher,
  );

  const from = data?.meta?.pagination?.from;
  const to = data?.meta?.pagination?.to;

  return {
    attendanceList: data?.data ?? [],
    employeeData: data?.included ?? [],
    totalCount: data?.meta?.pagination?.count ?? 0,
    isLoading,
    error,
    mutate,
    pagination: {
      firstResult: from,
      lastResult: to,
      limit: Number(limit),
      page: Number(page),
    },
    meta: data?.meta,
  };
};
