"use client";

import { ApprovalRequestData, ApprovalStep } from "../type/approval-request";
import { TIncludedEmployee } from "../type/employee-leaves";
import { getIncludedItem } from "../utils/included-data/getIncludedItem";

export function useApprovalWorkflow(
  item: unknown,
  included: (TIncludedEmployee | ApprovalRequestData)[],
) {
  const approvalRel = (
    item as { relationships?: { approval_request?: { data?: { id: string } } } }
  )?.relationships?.approval_request?.data;

  if (!approvalRel?.id) {
    return { steps: [], currentStepId: undefined, approvalRequest: null };
  }

  const approvalRequest = getIncludedItem(
    included,
    "approval_request",
    approvalRel.id,
  ) as ApprovalRequestData;

  const steps: ApprovalStep[] = approvalRequest?.attributes?.steps || [];
  const currentStepId = approvalRequest?.attributes?.current_step?.id;

  return { steps, currentStepId, approvalRequest };
}
