import useSWR from "swr";
import {
  TEmployeeAttributes,
  TEmployeeData,
  TEmployeeResponse,
  TUserRoleIncluded,
} from "../../type/employee";
import { TSalaryPackageData } from "../../type/salary-package";
import { fetcher } from "@/services/fetcher";
import { useSalaryPackageData } from "./useSalaryPackageData";
import { KeyedMutator } from "swr";

export const useEmployeeDetails = (id: string) => {
  const {
    data,
    error,
    isLoading: swrIsLoading,
    mutate,
  } = useSWR<TEmployeeResponse>(
    id
      ? `/api/employees/${id}?include=user_roles.role,user_roles.project,salary_package.approval_request,draft_salary_package.approval_request,pending_salary_package.approval_request`
      : null,
    fetcher,
  );
  const isLoading = !id || swrIsLoading;
  const employee: TEmployeeAttributes | null = data?.data?.attributes || null;
  const employeeData: TEmployeeData | null = data?.data || null;

  // Get all included data
  const included = data?.included || [];

  // Get user roles from included data
  const userRoles: TUserRoleIncluded[] = included.filter(
    (item): item is TUserRoleIncluded => item.type === "user_role",
  );

  const historySalaryPackage: TSalaryPackageData[] = [];

  // Process salary package data using the dedicated hook
  const {
    salaryPackage,
    editableSalaryPackage,
    pendingSalaryPackage,
    submittedSalaryPackage,
    salaryPackageStatus,
  } = useSalaryPackageData(included);

  return {
    employee,
    employeeData,
    salaryPackage,
    editableSalaryPackage,
    submittedSalaryPackage,
    salaryPackageStatus,
    historySalaryPackage,
    userRoles,
    included,
    isLoading,
    error,
    mutate: mutate as KeyedMutator<TEmployeeResponse>,
  };
};
