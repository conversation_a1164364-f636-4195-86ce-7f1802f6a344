import { useOptimisticMutation } from "@/hooks/ues-optimistic-mutatation";
import { useApprovalMutations } from "./useApprovalMutations";
import { fetcher } from "@/services/fetcher";
import { EmployeeLeavesResponse, LeaveDetail } from "../type/employee-leaves";
import { ApprovalStep, ApprovalRequestData } from "../type/approval-request";
import { getIncludedItem } from "../utils/included-data/getIncludedItem";

interface UseLeaveRequestMutationsProps {
  key: string;
}

type MutationParams = {
  id: string;
  leaveId?: string;
  comment?: string;
  approvalRequestId?: string;
  employeeId?: string;
};

export const useLeaveRequestMutations = ({
  key,
}: UseLeaveRequestMutationsProps) => {
  const {
    approveRequest: approveLeaveRequest,
    rejectRequest: rejectLeaveRequest,
    withdrawLeaveRequest,
    isApproving,
    isRejecting,
    isWithdrawing,
  } = useApprovalMutations();

  const result = useOptimisticMutation<
    EmployeeLeavesResponse,
    MutationParams,
    EmployeeLeavesResponse
  >({
    key,
    fetcher: fetcher,
    mutations: {
      acceptLeaveRequest: {
        updateFn: (data, params) => {
          const leaveId = params.leaveId || params.id;
          if (!data) return data;

          const targetLeave = data.data.find(
            (leave: LeaveDetail) => leave.id === leaveId,
          );
          const approvalRequestId =
            targetLeave?.relationships?.approval_request?.data?.id;

          const approvalRequest = approvalRequestId
            ? (getIncludedItem(
                data.included ?? [],
                "approval_request",
                approvalRequestId,
              ) as ApprovalRequestData | undefined)
            : undefined;

          return {
            ...data,
            data: data.data.map((leave: LeaveDetail) => {
              if (leave.id === leaveId && approvalRequest) {
                const currentStep = approvalRequest.attributes.current_step;
                const steps = approvalRequest.attributes.steps || [];
                const currentStepIndex = steps.findIndex(
                  (step: ApprovalStep) => step.id === currentStep?.id,
                );
                const nextStepIndex = currentStepIndex + 1;
                const isWorkflowComplete = nextStepIndex >= steps.length;

                return {
                  ...leave,
                  attributes: {
                    ...leave.attributes,
                    status: isWorkflowComplete ? "approved" : "pending",
                  },
                };
              }
              return leave;
            }),
            included: approvalRequestId
              ? data.included?.map((item) => {
                  if (
                    item.id === approvalRequestId &&
                    item.type === "approval_request"
                  ) {
                    const approvalItem = item as ApprovalRequestData;
                    const currentStep = approvalItem.attributes.current_step;
                    const steps = approvalItem.attributes.steps || [];

                    const currentStepIndex = steps.findIndex(
                      (step: ApprovalStep) => step.id === currentStep?.id,
                    );

                    const newAction = {
                      id: `temp-approve-${leaveId}`,
                      user_id: "current_user",
                      action: "approve" as const,
                      comment: params.comment || "",
                      created_at: "pending",
                    };

                    const updatedSteps = steps.map((step: ApprovalStep) => {
                      if (step.id === currentStep?.id) {
                        return {
                          ...step,
                          complete: true,
                          rejected: false,
                          actions: [...(step.actions || []), newAction],
                        };
                      }
                      return step;
                    });

                    const nextStepIndex = currentStepIndex + 1;
                    const nextStep =
                      nextStepIndex < steps.length
                        ? steps[nextStepIndex]
                        : null;
                    const isWorkflowComplete = nextStep === null;

                    return {
                      ...approvalItem,
                      attributes: {
                        ...approvalItem.attributes,
                        status: isWorkflowComplete ? "approved" : "pending",
                        current_step: nextStep,
                        steps: updatedSteps,
                      },
                    };
                  }
                  return item;
                }) || []
              : data.included,
          } as EmployeeLeavesResponse;
        },
        mutationFn: async (params) => {
          try {
            await approveLeaveRequest(params.id, params.comment || "");
            return { data: undefined };
          } catch (error) {
            return {
              error:
                error instanceof Error
                  ? error
                  : new Error("Failed to approve leave request"),
            };
          }
        },
      },
      rejectLeaveRequest: {
        updateFn: (data, params) => {
          const leaveId = params.leaveId || params.id;
          if (!data) return data;

          const targetLeave = data.data.find(
            (leave: LeaveDetail) => leave.id === leaveId,
          );
          const approvalRequestId =
            targetLeave?.relationships?.approval_request?.data?.id;

          const approvalRequest = approvalRequestId
            ? (getIncludedItem(
                data.included ?? [],
                "approval_request",
                approvalRequestId,
              ) as ApprovalRequestData | undefined)
            : undefined;

          return {
            ...data,
            data: data.data.map((leave: LeaveDetail) =>
              leave.id === leaveId
                ? {
                    ...leave,
                    attributes: {
                      ...leave.attributes,
                      status: "rejected",
                    },
                  }
                : leave,
            ),
            included: approvalRequest
              ? data.included?.map((item) => {
                  if (
                    item.id === approvalRequestId &&
                    item.type === "approval_request"
                  ) {
                    const approvalItem = item as ApprovalRequestData;
                    const currentStep = approvalItem.attributes.current_step;
                    const steps = approvalItem.attributes.steps || [];

                    const newAction = {
                      id: `temp-reject-${leaveId}`,
                      user_id: "current_user",
                      action: "reject" as const,
                      comment: params.comment || "",
                      created_at: "pending",
                    };

                    const updatedSteps = steps.map((step: ApprovalStep) => {
                      if (step.id === currentStep?.id) {
                        return {
                          ...step,
                          complete: false,
                          rejected: true,
                          actions: [...(step.actions || []), newAction],
                        };
                      }
                      return step;
                    });

                    return {
                      ...approvalItem,
                      attributes: {
                        ...approvalItem.attributes,
                        status: "rejected",
                        current_step: null,
                        steps: updatedSteps,
                      },
                    };
                  }
                  return item;
                }) || []
              : data.included,
          } as EmployeeLeavesResponse;
        },
        mutationFn: async (params) => {
          try {
            await rejectLeaveRequest(params.id, params.comment || "");
            return { data: undefined };
          } catch (error) {
            return {
              error:
                error instanceof Error
                  ? error
                  : new Error("Failed to reject leave request"),
            };
          }
        },
      },
      withdrawLeaveRequest: {
        updateFn: (data, params) => {
          const leaveId = params.leaveId || params.id;
          if (!data) return data;

          return {
            ...data,
            data: data.data.map((leave: LeaveDetail) =>
              leave.id === leaveId
                ? {
                    ...leave,
                    attributes: {
                      ...leave.attributes,
                      status: "withdrawn",
                    },
                  }
                : leave,
            ),
          };
        },
        mutationFn: async (params) => {
          try {
            if (!params.employeeId) {
              throw new Error(
                "Employee ID is required for withdrawing leave request",
              );
            }

            await withdrawLeaveRequest(params.id, params.employeeId);
            return { data: undefined };
          } catch (error) {
            return {
              error:
                error instanceof Error
                  ? error
                  : new Error("Failed to withdraw leave request"),
            };
          }
        },
      },
    },
    defaultData: {
      data: [],
      meta: {
        pagination: {
          count: 0,
          page: 1,
          limit: 10,
          from: 0,
          to: 0,
        },
      },
    },
    swrOptions: {
      dedupingInterval: 2000,
      revalidateAfterMutation: false,
      revalidateOnFocus: false,
    },
  });

  return {
    ...result,
    isApproving,
    isRejecting,
    isWithdrawing,
  };
};
