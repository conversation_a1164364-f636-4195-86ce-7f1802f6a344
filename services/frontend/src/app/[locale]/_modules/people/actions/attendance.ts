// app/[locale]/_modules/people/actions/attendance.ts
"use server";

import { peopleService } from "@/services/api/people";
import { ActionState } from "@/types";
import {
  createAttendanceSchema,
  CreateAttendanceSchemaType,
} from "../schemas/attendanceSchema";
import { handleError } from "@/lib/utils";
import { isValidationSuccess, validateFormData } from "@/lib/form-utils";

export async function createAttendance(
  employeeId: string | undefined,
  _prevState: ActionState<{ id: string }>,
  formData: FormData
): Promise<ActionState<{ id: string }>> {
  try {
    if (!employeeId) {
      return { error: "Employee ID is required", success: "", issues: [], data: null };
    }

    // 1) “Unwrap” attendance_event[...] → flat keys
    const flat = new FormData();
    for (const [key, value] of formData.entries()) {
     const m = key.match(/^attendance_event\[(.+)\]$/);
      if (m) {
        // convert `notes` → `note` so Zod schema (which expects `note`) will accept it
        const field = m[1] === "notes" ? "note" : m[1];
        flat.append(field, value as any);
      } else {
        flat.append(key, value as any);
      }
    }

    // 2) Ensure employee_id override
    flat.set("employee_id", employeeId);

    // 3) Validate against your zod schema
    const validation = (await validateFormData(
      flat,
      createAttendanceSchema
    )) as { success: true; data: CreateAttendanceSchemaType } | ActionState<CreateAttendanceSchemaType>;

    if (!isValidationSuccess(validation)) {
      return {
        error: validation.error,
        issues: validation.issues,
        data: null,
      };
    }

    // 4) Call your service with the **flat** data (the service will re-wrap)
    const response = await peopleService.createAttendance(employeeId, flat);
    const typed = response as { data?: { id: string } };

    return {
      success: "Attendance record created successfully",
      error: "",
      issues: [],
      data: { id: typed.data?.id || "" },
    };
  } catch (err) {
    return handleError(err, "Failed to create attendance record", []);
  }
}
