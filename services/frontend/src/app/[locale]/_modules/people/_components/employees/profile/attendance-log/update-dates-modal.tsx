"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import ResponsiveDialog from "@/components/responsive-dialog";
import { DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { DateRange } from "react-day-picker";
import { ar, enUS } from "date-fns/locale";
import { useLocale, useTranslations } from "next-intl";
import { LANGUAGES } from "@/constants/enum";
import { formatDate } from "@/lib/dateFormatter";
import { createDateWithoutTimezoneIssue } from "@/lib/utils";

type UpdateDatesModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onUpdate: (startDate: Date, endDate: Date) => void;
  initialStartDate?: string | Date;
  initialEndDate?: string | Date;
  isLoading: boolean;
  mode?: "update" | "create";
  title?: string;
  description?: string;
  submitButtonText?: string;
};

export function UpdateDatesModal({
  isOpen,
  onClose,
  onUpdate,
  initialStartDate,
  initialEndDate,
  isLoading,
  mode = "update",
  title,
  description,
  submitButtonText,
}: UpdateDatesModalProps) {
  const initialStartDateObj = createDateWithoutTimezoneIssue(initialStartDate);
  const initialEndDateObj = createDateWithoutTimezoneIssue(initialEndDate);

  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: initialStartDateObj,
    to: initialEndDateObj,
  });

  const [selectedDate, setSelectedDate] = useState<Date>(initialStartDateObj);

  const [time, setTime] = useState<string>(
    initialStartDateObj
      ? `${String(initialStartDateObj.getHours()).padStart(2, "0")}:${String(initialStartDateObj.getMinutes()).padStart(2, "0")}`
      : "09:00",
  );

  const locale = useLocale() as Locale;
  const isAr = locale === LANGUAGES.ARABIC;
  const t = useTranslations();

  const [error, setError] = useState<string | null>(null);

  const handleSubmit = () => {
    setError(null);

    if (!selectedDate || !time) {
      setError(
        t(
          "people.employees-page.profile.attendance-log.update-dates.errors.dates-required",
        ),
      );
      return;
    }

    const today = new Date(new Date().setHours(0, 0, 0, 0));
    if (mode === "create" && selectedDate < today) {
      setError(
        t(
          "people.employees-page.profile.attendance-log.update-dates.errors.past-date",
        ),
      );
      return;
    }
    const [hh, mm] = time.split(":").map(Number);
    const dt = new Date(selectedDate);
    dt.setHours(hh, mm, 0, 0);
    onUpdate(dt, dt);
  };

  const modalTitle =
    title ||
    (mode === "update"
      ? t("people.employees-page.profile.attendance-log.update-dates.title")
      : t(
          "people.employees-page.profile.attendance-log.create-attendance-modal.select-dates",
        ));

  const modalDescription =
    description ||
    (mode === "update"
      ? t(
          "people.employees-page.profile.attendance-log.update-dates.description",
        )
      : t(
          "people.employees-page.profile.attendance-log.create-attendance-modal.select-dates-description",
        ));

  const buttonText =
    submitButtonText ||
    (mode === "update"
      ? t(
          "people.employees-page.profile.attendance-log.update-dates.update-button",
        )
      : t(
          "people.employees-page.profile.attendance-log.create-attendance-modal.select-dates-button",
        ));

  return (
    <ResponsiveDialog
      open={isOpen}
      onOpenChange={onClose}
      header={
        <div className="px-6 py-5 border-b bg-white sticky top-0 z-10 rounded-2xl">
          <DialogTitle className="text-lg font-semibold">
            {modalTitle}
          </DialogTitle>
          <DialogDescription className="text-sm text-gray-500 mt-1">
            {modalDescription}
          </DialogDescription>
        </div>
      }
      className="max-w-[700px] rounded-2xl"
    >
      <div className="w-full mb-6">
        <div className="border rounded-md p-4 bg-white w-full">
          <Calendar
            locale={isAr ? ar : enUS}
            mode={mode === "create" ? "single" : "range"}
            selected={mode === "create" ? selectedDate : dateRange}
            onSelect={(d) => {
              if (!d) return;
              setSelectedDate(d as Date);
              if (mode !== "create") {
                // لو range: cast ونظّم
                const r = d as DateRange;
                setDateRange(r);
              }
            }}
            numberOfMonths={mode === "create" ? 1 : 2}
            disabled={(date) =>
              date < new Date(new Date().setHours(0, 0, 0, 0))
            }
            showOutsideDays={false}
            classNames={{
              root: "w-full",
              months:
                "flex gap-3 flex-col md:flex-row space-y-4 md:space-x-4 md:space-y-0 w-full justify-center",
              month: "space-y-4 w-full",
              caption: "flex justify-center pt-1 relative items-center",
              caption_label: "text-sm font-medium",
              nav: "space-x-1 flex items-center",
              nav_button:
                "h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100",
              nav_button_previous: "absolute left-1",
              nav_button_next: "absolute right-1",
              table: "w-full border-collapse space-y-1",
              head_row: "flex w-full justify-between",
              head_cell:
                "text-muted-foreground rounded-md w-9 font-normal text-[0.8rem] text-center",
              row: "flex w-full mt-2 justify-between",
              cell: "h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
              day: "h-9 w-9 p-0 font-normal aria-selected:opacity-100",
              day_selected:
                "bg-secondary text-primary-foreground hover:bg-primary rounded-sm hover:text-primary-foreground focus:bg-secondary focus:text-primary-foreground",
              day_today: "bg-gray-200 rounded-sm text-accent-foreground",
              day_outside: "text-muted-foreground opacity-50",
              day_disabled: "text-muted-foreground opacity-50",
              day_range_middle:
                "aria-selected:bg-accent aria-selected:text-accent-foreground",
              day_hidden: "invisible",
            }}
          />
        </div>

        {mode === "create" && (
          <div className="mt-3 flex gap-2 items-center">
            <label className="text-sm font-medium">
              {t(
                "people.employees-page.profile.attendance-log.update-dates.time",
              )}
              :
            </label>
            <input
              type="time"
              className="border rounded px-2 py-1"
              value={time}
              onChange={(e) => setTime(e.target.value)}
            />
          </div>
        )}
        {error && (
          <div className="w-full mt-4">
            <p className="text-sm text-red-500 p-2 bg-red-50 rounded-md border border-red-100">
              {error}
            </p>
          </div>
        )}
      </div>

      <div className="bg-gray-50 p-4 rounded-md border border-gray-100">
        <h3 className="text-sm font-medium mb-3">
          {t(
            "people.employees-page.profile.attendance-log.update-dates.selected-range",
          )}
        </h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-xs text-gray-500">
              {t(
                "people.employees-page.profile.attendance-log.update-dates.start-date",
              )}
              :
            </p>
            <p className="text-sm font-medium">
              {dateRange?.from
                ? formatDate(dateRange.from, locale)
                : t(
                    "people.employees-page.profile.attendance-log.update-dates.none",
                  )}
            </p>
          </div>
          <div>
            <p className="text-xs text-gray-500">
              {t(
                "people.employees-page.profile.attendance-log.update-dates.end-date",
              )}
              :
            </p>
            <p className="text-sm font-medium">
              {dateRange?.to
                ? formatDate(dateRange.to, locale)
                : t(
                    "people.employees-page.profile.attendance-log.update-dates.none",
                  )}
            </p>
          </div>
        </div>
      </div>

      <div className="flex justify-center md:justify-end gap-3 max-md:px-6 pt-4 border-t bg-white sticky bottom-0 left-0 right-0 z-10 shadow-sm">
        <Button
          variant="outline"
          onClick={onClose}
          disabled={isLoading}
          className="min-w-[100px] min-h-12 w-3/6 md:w-10"
        >
          {t("common.buttonText.cancel")}
        </Button>
        <Button
          onClick={handleSubmit}
          disabled={isLoading}
          className="min-w-[100px] min-h-12 w-3/6 md:w-2/6"
        >
          {isLoading
            ? t(
                "people.employees-page.profile.attendance-log.update-dates.updating",
              )
            : buttonText}
        </Button>
      </div>
    </ResponsiveDialog>
  );
}
