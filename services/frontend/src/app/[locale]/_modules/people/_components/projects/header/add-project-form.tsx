"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { Loader } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { TFunction } from "@/types";
import { ProjectSchemaType, projectSchema } from "../../../schemas/projectSchema";
import { TProject } from "@/types/core/project";
import { useToastMessage } from "@/hooks/use-toast-message";

type AddProjectFormProps = {
  mode?: "add" | "edit";
  project?: TProject | null;
  onSuccess?: () => void;
  onCancel?: () => void;
};

export default function AddProjectForm({
  mode = "add",
  project = null,
  onSuccess,
  onCancel,
}: AddProjectFormProps = {}) {
  const t = useTranslations() as TFunction;
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { showToast } = useToastMessage();

  const form = useForm<ProjectSchemaType>({
    resolver: zodResolver(projectSchema),
    defaultValues: {
      name: project?.attributes?.name || "",
      description: project?.attributes?.description || "",
      status: project?.attributes?.status || "active",
    },
  });

  const onSubmit = async (data: ProjectSchemaType) => {
    setIsSubmitting(true);
    try {
      // TODO: Implement actual API call
      console.log("Project data:", data);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      showToast("success", mode === "add"
        ? t("projects.page.create.success")
        : t("projects.page.update.success"));
      
      onSuccess?.();
    } catch (error) {
      console.error("Error saving project:", error);
      showToast("error", mode === "add"
        ? t("projects.page.create.error")
        : t("projects.page.update.error"));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-white rounded-lg max-w-2xl mx-auto w-[99.5%]">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("projects.page.form.name.label")}</FormLabel>
                <FormControl>
                  <Input
                    placeholder={t("projects.page.form.name.placeholder")}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("projects.page.form.description.label")}</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder={t("projects.page.form.description.placeholder")}
                    className="min-h-[100px]"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("projects.page.form.status.label")}</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder={t("projects.page.form.status.placeholder")} />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="active">
                      {t("projects.page.form.status.active")}
                    </SelectItem>
                    <SelectItem value="inactive">
                      {t("projects.page.form.status.inactive")}
                    </SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="sticky border-t border-t-slate-200 pt-2.5 bottom-0 left-0 w-[100%] rounded-t-[18px] bg-white flex flex-row items-center justify-between gap-4 sm:gap-6">
            <Button
              type="submit"
              disabled={isSubmitting}
              className="px-4 py-2 w-full h-12 sm:max-w-[244px] rounded-lg"
            >
              {isSubmitting ? (
                <Loader className="animate-spin" />
              ) : mode === "add" ? (
                t("common.buttonText.add")
              ) : (
                t("common.buttonText.update")
              )}
            </Button>
            <Button
              variant="outline"
              type="button"
              className="w-full h-12 sm:max-w-[244px] rounded-lg"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              {t("common.buttonText.cancel2")}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
