"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { PlusCircle } from "../../../../../../../../public/images/icons";
import { useTranslations } from "next-intl";
import { TFunction } from "@/types";

type ProjectsHeaderProps = {
  title: string;
  onCreateProject?: () => void;
};

const ProjectsHeader = ({ title, onCreateProject }: ProjectsHeaderProps) => {
  const t = useTranslations() as TFunction;

  return (
    <div className="flex items-center justify-between mb-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">
          {title}
        </h1>
        <p className="text-gray-600 mt-1">
          {t("projects.page.description")}
        </p>
      </div>
      <Button
        onClick={onCreateProject}
        className="flex items-center gap-2"
      >
        <PlusCircle className="h-4 w-4" />
        {t("projects.page.create.title")}
      </Button>
    </div>
  );
};

export default ProjectsHeader;
