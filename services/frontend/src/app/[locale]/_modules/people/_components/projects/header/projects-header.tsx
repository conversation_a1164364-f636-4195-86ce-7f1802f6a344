"use client";

import LoaderPortal from "@/components/loader/loader-portal";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { DialogDescription, DialogTitle } from "@/components/ui/dialog";
import { TFunction } from "@/types";
import { useTranslations } from "next-intl";
import dynamic from "next/dynamic";
import { useState } from "react";
import { PlusCircle } from "../../../../../../../../public/images/icons";

const ResponsiveDialog = dynamic(
  () => import("@/components/responsive-dialog"),
  {
    ssr: false,
    loading: () => <LoaderPortal overlayColor="#000" overlayOpacity={0.6} />,
  },
);

type ProjectsHeaderProps = {
  title: string;
};

const ProjectsHeader = ({ title }: ProjectsHeaderProps) => {
  return (
    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
      <h2 className="max-w-[301px] leading-[120%] text-base text-secondary mt-1 mb-2 sm:mb-0.5">
        {title}
      </h2>
      <AddNewProjectDialog />
    </div>
  );
};

const AddNewProjectDialog = () => {
  const t = useTranslations() as TFunction;
  const [isAddNewProject, setIsAddNewProject] = useState<boolean>(false);

  return (
    <div className="max-md:pt-2 md:pb-5 flex max-md:flex-col items-start md:items-center md:justify-between gap-4">
      <Button
        onClick={() => setIsAddNewProject((prev) => !prev)}
        className="sm:min-w-[169px] min-h-12 max-h-12 shadow-none font-semibold text-base max-md:mb-8 flex gap-4 items-center"
      >
        <PlusCircle />
        {t("projects.page.create.title")}
      </Button>
      {isAddNewProject && (
        <ResponsiveDialog
          open={isAddNewProject}
          onOpenChange={setIsAddNewProject}
          closeBtnStyle="top-[21px]"
          header={
            <>
              <DialogTitle className="px-6 py-[22px] border-b font-semibold text-[18px] leading-[28px]">
                {t("projects.page.create.dialog.title")}
              </DialogTitle>
              <DialogDescription className="sr-only">
                {t("projects.page.create.dialog.description")}
              </DialogDescription>
            </>
          }
        >
          {/* TODO: Add ProjectForm component */}
          <div className="p-6">
            <p>Project form will be implemented here</p>
          </div>
        </ResponsiveDialog>
      )}
    </div>
  );
};

export default ProjectsHeader;
