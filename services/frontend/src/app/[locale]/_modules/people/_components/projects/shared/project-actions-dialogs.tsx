"use client";

import { TProject } from "@/types/core/project";
import dynamic from "next/dynamic";
import LoaderPortal from "@/components/loader/loader-portal";

const DeleteProjectDialog = dynamic(() => import("../delete-project-dialog"), {
  ssr: false,
  loading: () => <LoaderPortal overlayColor="#000" overlayOpacity={0.6} />,
});
const EditProjectDialog = dynamic(() => import("../edit-project-dialog"), {
  ssr: false,
  loading: () => <LoaderPortal overlayColor="#000" overlayOpacity={0.6} />,
});

type ProjectActionsState = {
  isEditDialogOpen: boolean;
  isDeleteDialogOpen: boolean;
  selectedProject: TProject | null;
};

type ProjectActionsHandlers = {
  handleEdit: (project: TProject) => void;
  handleDelete: (project: TProject) => void;
  handleConfirmDelete?: () => Promise<void>;
  handleUpdateSuccess?: (updatedProject: TProject) => void;
  closeEditDialog: () => void;
  closeDeleteDialog: () => void;
};

type ProjectActionsDialogsProps = {
  state: ProjectActionsState;
  handlers: ProjectActionsHandlers;
};

const ProjectActionsDialogs = ({
  state,
  handlers,
}: ProjectActionsDialogsProps) => {
  return (
    <>
      {/* Edit Project Dialog */}
      {state.isEditDialogOpen && state.selectedProject && (
        <EditProjectDialog
          project={state.selectedProject}
          isOpen={state.isEditDialogOpen}
          onClose={handlers.closeEditDialog}
          onSuccess={handlers.handleUpdateSuccess}
        />
      )}

      {state.isDeleteDialogOpen && state.selectedProject && (
        <DeleteProjectDialog
          project={state.selectedProject}
          isOpen={state.isDeleteDialogOpen}
          onClose={handlers.closeDeleteDialog}
          {...(handlers.handleConfirmDelete && {
            onConfirmDelete: handlers.handleConfirmDelete,
          })}
        />
      )}
    </>
  );
};

export default ProjectActionsDialogs;
