import React from "react";
import ResponsiveDialog from "@/components/responsive-dialog";
import { useApprovalWorkflow } from "../hooks/useApprovalWorkflow";
import { TIncludedEmployee } from "../type/employee-leaves";
import ApprovalStepsIndicator from "@/components/approval-steps-indicator";
import { useTranslations } from "next-intl";
import { DialogTitle } from "@/components/ui/dialog";
import { ApprovalRequestData } from "../type/approval-request";

type ApprovalWorkflowModalProps<T> = {
  isOpen: boolean;
  onClose: () => void;
  data: T;
  included: (TIncludedEmployee | ApprovalRequestData)[];
  type: "leave" | "salary" | string; // extendable for more types
};

const ApprovalWorkflowModal = <T,>({
  isOpen,
  onClose,
  data,
  included,
  type,
}: ApprovalWorkflowModalProps<T>) => {
  const { steps, approvalRequest, currentStepId } = useApprovalWorkflow(
    data,
    included,
  );
  const t = useTranslations();

  const status = approvalRequest?.attributes.status;

  const getStatus = () => {
    if (status === "approved") return "approved";
    if (status === "rejected") return "rejected";
    return "pending";
  };

  const getTitle = () => {
    return t(
      "people.leaves-requests-page.request-details.approval-workflow.title",
    );
  };

  const header = (
    <div className="px-6 py-[22px] border-b border-gray-100">
      <DialogTitle className="text-lg font-semibold text-right">
        {getTitle()}
      </DialogTitle>
    </div>
  );

  return (
    <ResponsiveDialog
      open={isOpen}
      onOpenChange={onClose}
      closeBtnStyle="top-[22px]"
      header={header}
      className="max-w-full sm:min-w-[469px] w-auto"
    >
      <>
        {steps && steps.length > 0 ? (
          <ApprovalStepsIndicator
            steps={steps}
            currentStepId={currentStepId}
            status={getStatus()}
            showTitle={false}
          />
        ) : (
          <div className="text-center py-8 text-gray-500">
            {t(
              "people.leaves-requests-page.request-details.approval-workflow.no-steps",
            )}
          </div>
        )}
      </>
    </ResponsiveDialog>
  );
};

export default ApprovalWorkflowModal;
