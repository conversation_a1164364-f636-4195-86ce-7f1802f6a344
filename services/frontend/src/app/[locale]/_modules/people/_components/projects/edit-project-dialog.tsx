"use client";

import ResponsiveDialog from "@/components/responsive-dialog";
import { useTranslations } from "next-intl";
import { TFunction } from "@/types";
import { DialogDescription, DialogTitle } from "@/components/ui/dialog";
import AddProjectForm from "./header/add-project-form";
import { TProject } from "@/types/core/project";

type EditProjectDialogProps = {
  project: TProject | null;
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (updatedProject: TProject) => void;
};

export default function EditProjectDialog({
  project,
  isOpen,
  onClose,
  onSuccess,
}: EditProjectDialogProps) {
  const t = useTranslations() as TFunction;

  if (!project) return null;

  return (
    <ResponsiveDialog
      open={isOpen}
      onOpenChange={onClose}
      closeBtnStyle="top-[21px]"
      header={
        <>
          <div className="px-6 py-[22px] border-b">
            <DialogTitle className="font-semibold text-[18px] leading-[28px]">
              {t("projects.page.update.title")}
            </DialogTitle>
            <DialogDescription className="text-sm text-gray-600 mt-1 sr-only">
              {t("projects.page.update.description")}
            </DialogDescription>
          </div>
        </>
      }
    >
      <AddProjectForm
        mode="edit"
        project={project}
        onSuccess={() => {
          onClose();
          onSuccess?.(project);
        }}
        onCancel={onClose}
      />
    </ResponsiveDialog>
  );
}
