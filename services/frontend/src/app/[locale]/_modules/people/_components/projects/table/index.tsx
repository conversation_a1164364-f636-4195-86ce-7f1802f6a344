"use client";

import { PaginationWithLinks } from "@/components/pagination-with-links";
import { DataTable } from "@/components/table";
import React, { useState } from "react";
import { columns } from "./projects-columns";
import { RowSelectionState } from "@tanstack/react-table";
import { useTranslations, useLocale } from "next-intl";
import { useProjects } from "@/hooks/useProjects";
import { useRouter } from "@/i18n/routing";
import { useSearchParams } from "next/navigation";
import { useTableRegistration } from "@/hooks/useTableRegistration";
import { TFunction } from "@/types";
import { Locale } from "@/i18n/routing";
import { TProject } from "@/types/core/project";
import { useProjectMutations } from "../../../hooks/projects/useProjectMutations";

type ProjectsTableProps = {
  showPagination: boolean;
  searchParams: {
    [key: string]: string | string[] | undefined;
  };
};

const ProjectsTable = ({
  showPagination = true,
  searchParams = { page: "1", limit: "5" },
}: ProjectsTableProps) => {
  const t = useTranslations() as TFunction;
  const locale = useLocale() as Locale;
  const router = useRouter();
  const urlSearchParams = useSearchParams();

  const limit = parseInt(
    Array.isArray(searchParams.limit)
      ? searchParams.limit[0] ?? "5"
      : searchParams.limit ?? "5",
    10
  );
  const page = parseInt(
    Array.isArray(searchParams.page)
      ? searchParams.page[0] ?? "1"
      : searchParams.page ?? "1",
    10
  );
  const search = urlSearchParams.get("search") || "";
  const sort = Array.isArray(searchParams.sort)
    ? searchParams.sort[0] ?? "id"
    : searchParams.sort ?? "id";

  const { projects, totalCount, pagination, isLoading, error } = useProjects(
    page,
    limit,
    sort
  );
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  useTableRegistration("projects", columns);

  return (
    <>
      <DataTable
        data={projects}
        dataCount={totalCount}
        columns={columns}
        tableContainerClass="min-h-[240px] text-start"
        title={t("projects.page.table.title")}
        meta={{
          t,
          locale,
        }}
        rowSelection={rowSelection}
        onRowSelectionChange={setRowSelection}
        translationPrefix="projects.page.table"
        tableId="projects"
        isLoading={isLoading}
        initialLimit={5}
        error={error}
        tableHeadStyle="text-start"
        tableCellStyle="text-start"
        hideColumns={true}
      />
      <div className="w-full pt-[18px]">
        {showPagination && (
          <PaginationWithLinks
            page={pagination.page ?? Number(page)}
            pageSize={Number(limit)}
            totalCount={Number(totalCount || 0)}
            firstLastCounts={{
              firstCount: pagination.firstResult ?? 1,
              lastCount: pagination.lastResult ?? 5,
            }}
            pageSizeSelectOptions={{
              pageSizeOptions: [5, 10, 25, 30, 45, 50],
              pageSizeSearchParam: "limit",
            }}
            isLoading={isLoading}
            isDisabled={!projects?.length}
          />
        )}
      </div>
    </>
  );
};

export default ProjectsTable;
