"use client";

import { PaginationWithLinks } from "@/components/pagination-with-links";
import { DataTable } from "@/components/table";
import React, { useState } from "react";
import { employeesSalariesColumns } from "../../shared/salary-columns";
import { RowSelectionState } from "@tanstack/react-table";
import { useLocale, useTranslations } from "next-intl";
import { SalaryCalculation } from "../../../type/employees-salaries";
import dynamic from "next/dynamic";
import LoaderPortal from "@/components/loader/loader-portal";
import { useSalaryMutations } from "../../../hooks/useSalaryMutations";
import { useTableRegistration } from "@/hooks/useTableRegistration";
import { useSearchParams } from "next/navigation";

const EmployeeSalariesDetailsDialog = dynamic(
  () => import("../employees-salaries-details-dialog"),
  {
    ssr: false,
    loading: () => <LoaderPortal overlayColor="#000" overlayOpacity={0.6} />,
  },
);
const EditSalaryModal = dynamic(() => import("../edit-total-salary-dialog"), {
  ssr: false,
  loading: () => <LoaderPortal overlayColor="#000" overlayOpacity={0.6} />,
});

type EmployeesSalariesTableProps = {
  showPagination: boolean;
  searchParams: {
    page: string;
    limit: string;
  };
};

const EmployeesSalariesTable = ({
  showPagination,
  searchParams,
}: EmployeesSalariesTableProps) => {
  const t = useTranslations();
  const locale = useLocale();
  const urlSearchParams = useSearchParams();

  const limit = parseInt(searchParams.limit ?? "5", 10);
  const page = parseInt(searchParams.page ?? "1", 10);
  const search = urlSearchParams.get("search") || "";
  const sort = Array.isArray(searchParams.sort)
    ? searchParams.sort[0] ?? "-period_start_date"
    : searchParams.sort ?? "-period_start_date";

  // Use the modernized salary mutations hook
  const {
    salaryCalculations,
    totalCount,
    employeesData,
    pagination,
    isLoading,
    error,
    submitSalary,
    approveSalary,
    rejectSalary,
    paySalary,
    regenerateSlip,
    isSubmitting,
    isApproving,
    isRejecting,
    isPaying,
    isRegenerating,
  } = useSalaryMutations({
    page,
    limit,
    sort,
    tableId: "employees-salaries",
  });

  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [showSalariesDetails, setShowSalariesDetails] = useState(false);
  const [selectedEmployeeSalary, setSelectedEmployeeSalary] =
    useState<SalaryCalculation | null>();
  const [showEditNoteDialog, setShowEditNoteDialog] = useState(false);
  const [showEditSalaryDialog, setShowEditSalaryDialog] = useState(false);

  // Register table for filtering
  useTableRegistration("employees-salaries", employeesSalariesColumns);

  // Handle submit salary (change from draft to submitted)
  const handleSubmitSalary = (salaryId: string) => {
    submitSalary(salaryId);
  };

  // Handle approve salary
  const handleApproveSalary = (salaryId: string) => {
    approveSalary(salaryId);
  };

  // Handle reject salary
  const handleRejectSalary = (salaryId: string) => {
    rejectSalary(salaryId);
  };

  // Handle pay salary
  const handlePaySalary = (salaryId: string) => {
    paySalary(salaryId);
  };

  // Handle regenerate slip
  const handleRegenerateSlip = (salaryId: string) => {
    regenerateSlip(salaryId);
  };

  const hanleShowSalariesDetails = (data: SalaryCalculation) => {
    setSelectedEmployeeSalary(data);
    setShowSalariesDetails(true);
  };

  const handleShowNotDialog = (data: SalaryCalculation) => {
    setSelectedEmployeeSalary(data);
    setShowEditNoteDialog(true);
  };

  return (
    <>
      <DataTable
        data={salaryCalculations}
        dataCount={totalCount}
        columns={employeesSalariesColumns}
        tableContainerClass="min-h-[240px]"
        title={t("people.employees-salaries-page.table.title")}
        meta={{
          t,
          locale,
          isSubmitting,
          isApproving,
          isRejecting,
          isPaying,
          isRegenerating,
          onSubmitSalary: handleSubmitSalary,
          onApproveSalary: handleApproveSalary,
          onRejectSalary: handleRejectSalary,
          onPaySalary: handlePaySalary,
          onRegenerateSlip: handleRegenerateSlip,
          onShowDetails: hanleShowSalariesDetails,
          onShowEditNote: handleShowNotDialog,
          employeesData: employeesData,
        }}
        rowSelection={rowSelection}
        onRowSelectionChange={setRowSelection}
        translationPrefix="people.employees-salaries-page.table"
        tableId="employee-salaries"
        isLoading={isLoading}
        error={error}
      />
      {showPagination && (
        <div className="w-full pt-[18px]">
          <PaginationWithLinks
            page={pagination?.page ?? Number(page)}
            pageSize={Number(limit)}
            totalCount={pagination?.count || 0}
            firstLastCounts={{
              firstCount: pagination?.from ?? 1,
              lastCount: pagination?.to ?? 5,
            }}
            pageSizeSelectOptions={{
              pageSizeOptions: [5, 10, 25, 30, 45, 50],
              pageSizeSearchParam: "limit",
            }}
            isLoading={isLoading}
            isDisabled={!salaryCalculations?.length}
          />
        </div>
      )}
      {showSalariesDetails && selectedEmployeeSalary && (
        <EmployeeSalariesDetailsDialog
          setShowSalariesDetails={setShowSalariesDetails}
          setShowEditSalaryDialog={setShowEditSalaryDialog}
          showSalariesDetails={showSalariesDetails}
          selectedEmployee={selectedEmployeeSalary as any}
        />
      )}

      {(showEditSalaryDialog || showEditNoteDialog) && (
        <EditSalaryModal
          mode={showEditSalaryDialog ? "salary" : "note"}
          currentTotalSalary={
            selectedEmployeeSalary?.attributes?.gross_salary ?? 0
          }
          currentNote={selectedEmployeeSalary?.attributes?.notes || ""}
          setShowEditSalaryDialog={() => {
            setShowEditSalaryDialog(false);
            setShowEditNoteDialog(false);
          }}
          showEditSalaryDialog={showEditSalaryDialog || showEditNoteDialog}
        />
      )}
    </>
  );
};

export default EmployeesSalariesTable;
