"use client";

import React, { useState } from "react";
import dynamic from "next/dynamic";
import { useTranslations, useLocale } from "next-intl";
import { useSearchParams } from "next/navigation";
import { DataTable } from "@/components/table";
import { PaginationWithLinks } from "@/components/pagination-with-links";
import { Button } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";
import LoaderPortal from "@/components/loader/loader-portal";
import { RowSelectionState } from "@tanstack/react-table";
import { columns } from "./attendance-requests-columns";
import { useTableRegistration } from "@/hooks/useTableRegistration";
import { useAttendanceEvents } from "@/app/[locale]/_modules/people/hooks/attendance/useAttendanceEvents";
const CreateAttendanceModal = dynamic(
  () =>
    import("./CreateAttendanceEventModal").then(
      (mod) => mod.CreateAttendanceEventModal
    ),
  {
    loading: () => (
      <LoaderPortal
        size={75}
        borderWidth={4}
        overlayColor="#000"
        overlayClassName="bg-opacity-50"
      />
    ),
    ssr: false,
  }
);

const AttendanceRequestsTable = ({
  showPagination = false,
  searchParams: serverSearchParams,
}: {
  showPagination?: boolean;
  searchParams?: { page: string; limit: string };
}) => {
  const t = useTranslations();
  const locale = useLocale();
  const clientSearchParams = useSearchParams();

  const limit =
    serverSearchParams?.limit ?? clientSearchParams.get("limit") ?? "5";
  const page =
    serverSearchParams?.page ?? clientSearchParams.get("page") ?? "1";

  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [isModalOpen, setIsModalOpen] = useState(false);

  const sort =
    serverSearchParams?.sort ??
    clientSearchParams.get("sort") ??
    "-timestamp";

  useTableRegistration("attendance-requests", columns);

  // Use the new attendance hook
  const {
    attendanceList,
    employeeData,
    totalCount,
    isLoading,
    error,
    mutate,
    pagination,
    meta,
  } = useAttendanceEvents(Number(page), Number(limit), sort);

  const firstResult = pagination?.firstResult || 1;
  const lastResult = pagination?.lastResult || 1;

  const CreateButton = (
    <Button
      onClick={() => setIsModalOpen(true)}
      className="min-h-11 shadow-none flex items-center justify-center "
    >
      <PlusCircle className="h-5 w-5" />
      {/* {t("people.attendance-requests-page.create")} */}
    </Button>
  );

  return (
    <>
      <DataTable
        data={attendanceList || []}
        columns={columns}
        tableContainerClass="min-h-[240px]"
        title={t("people.attendance-requests-page.table.title")}
        meta={{ t, locale, employeeData }}
        rowSelection={rowSelection}
        onRowSelectionChange={setRowSelection}
        translationPrefix="people.attendance-requests-page.table"
        tableId="attendance-requests"
        isLoading={isLoading}
        initialLimit={Number(limit)}
        error={error}
        dataCount={totalCount}
        hideSearch={false}
        hideFilters={false}
        headerActions={CreateButton}
        exportConfig={{
          entity: "attendance-events",
          enabled: true,
          enhanced: true,
          customFilters: { page: 1, limit: 1000 },
          columnCategories: {
            id: "Basic",
            employee_id: "Employee Info",
            type: "Record Details",
            timestamp: "Time & Date",
            event_type: "Event Details",
            activity_type: "Event Details",
            location: "Location",
            notes: "Additional Info",
            inferred_type: "Additional Info",
            created_at: "Timestamps",
            updated_at: "Timestamps",
          },
          requiredColumns: ["id", "timestamp", "event_type"],
          excludeColumns: [
            "select",
            "actions",
          ],
          defaultSelectedColumns: [
            "id",
            "employeeName",
            "timestamp",
            "event_type",
            "activity_type",
            "location",
            "notes",
          ],
        }}
      />

      {showPagination && (
        <div className="w-full pt-[18px]">
          <PaginationWithLinks
            page={Number(page)}
            pageSize={Number(limit)}
            totalCount={totalCount}
            firstLastCounts={{ firstCount: firstResult, lastCount: lastResult }}
            isLoading={isLoading}
            isDisabled={!attendanceList?.length}
            pageSizeSelectOptions={{
              pageSizeOptions: [5, 10, 25, 30, 45, 50],
              pageSizeSearchParam: "limit",
            }}
          />
        </div>
      )}

      {isModalOpen && (
        <CreateAttendanceModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          showEmployeeField={true}
          onSuccess={() => {
            setIsModalOpen(false);
            mutate();
          }}
        />
      )}
    </>
  );
};

export default AttendanceRequestsTable;