"use client";

import { ColumnDef } from "@tanstack/react-table";
import { TProject } from "@/types/core/project";
import { TFunction } from "@/types";
import { Locale } from "@/i18n/routing";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { EllipsisVertical, Edit2, Trash } from "../../../../../../../../public/images/icons";

type ProjectsColumnMeta = {
  t: TFunction;
  locale: Locale;
  onEditProject?: (project: TProject) => void;
  onDeleteProject?: (projectId: string) => void;
  isDeleting?: boolean;
};

export const projectsColumns: ColumnDef<TProject, any>[] = [
  {
    accessorKey: "attributes.name",
    header: ({ column, table }) => {
      const meta = table.options.meta as ProjectsColumnMeta;
      return meta.t("projects.page.table.columns.name");
    },
    cell: ({ row }) => {
      const name = row.original.attributes.name;
      return (
        <div className="font-medium text-gray-900">
          {name}
        </div>
      );
    },
  },
  {
    accessorKey: "attributes.description",
    header: ({ column, table }) => {
      const meta = table.options.meta as ProjectsColumnMeta;
      return meta.t("projects.page.table.columns.description");
    },
    cell: ({ row }) => {
      const description = row.original.attributes.description;
      return (
        <div className="text-gray-600 max-w-xs truncate">
          {description || "-"}
        </div>
      );
    },
  },
  {
    accessorKey: "attributes.status",
    header: ({ column, table }) => {
      const meta = table.options.meta as ProjectsColumnMeta;
      return meta.t("projects.page.table.columns.status");
    },
    cell: ({ row }) => {
      const status = row.original.attributes.status;
      const isActive = status === "active";
      
      return (
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            isActive
              ? "bg-green-100 text-green-800"
              : "bg-gray-100 text-gray-800"
          }`}
        >
          {isActive ? "Active" : "Inactive"}
        </span>
      );
    },
  },
  {
    id: "actions",
    header: ({ table }) => {
      const meta = table.options.meta as ProjectsColumnMeta;
      return meta.t("projects.page.table.columns.actions");
    },
    cell: ({ row, table }) => {
      const project = row.original;
      const meta = table.options.meta as ProjectsColumnMeta;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <EllipsisVertical className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem
              onClick={() => meta.onEditProject?.(project)}
              className="cursor-pointer"
            >
              <Edit2 className="mr-2 h-4 w-4" />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => meta.onDeleteProject?.(project.id)}
              className="cursor-pointer text-red-600"
              disabled={meta.isDeleting}
            >
              <Trash className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
