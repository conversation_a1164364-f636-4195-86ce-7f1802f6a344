"use client";

import { Checkbox } from "@/components/ui/checkbox";
import { TFunction } from "@/types";
import { ColumnDef, TableMeta } from "@tanstack/react-table";
import { TProject } from "@/types/core/project";
import { Locale } from "@/i18n/routing";
import ProjectActions from "./project-actions";

interface TableMetaWithTranslation extends TableMeta<TProject> {
  t: TFunction;
  locale?: Locale;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const getMeta = (table: any) => table.options.meta as TableMetaWithTranslation;

export const columns: ColumnDef<TProject>[] = [
  {
    id: "select",
    enableSorting: false,
    enableHiding: false,
    header: ({ table }) => (
      <Checkbox
        className="w-[11px] h-[11px] rounded-[3px] border-[#ACBCBB] [&>*:last-child]:scale-[0.5] [&>*:last-child]:stroke-[1.5] flex items-center justify-center p-0"
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        className="w-[11px] h-[11px] rounded-[3px] border-[#1A1C1E] [&>*:last-child]:scale-[0.5] [&>*:last-child]:stroke-[1.5] flex items-center justify-center p-0"
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
  },
  {
    accessorKey: "name",
    enableColumnFilter: true,
    meta: {
      filterType: "text",
    },
    header: ({ table }) => {
      const { t } = getMeta(table);
      return <div>{t("projects.page.table.columns.name")}</div>;
    },
    cell: ({ row }) => {
      const project = row.original;
      return (
        <p className="text-sm font-semibold text-gray-900">
          {project.attributes.name}
        </p>
      );
    },
  },
  {
    accessorKey: "description",
    enableColumnFilter: true,
    meta: {
      filterType: "text",
    },
    header: ({ table }) => {
      const { t } = getMeta(table);
      return <div>{t("projects.page.table.columns.description")}</div>;
    },
    cell: ({ row }) => {
      const project = row.original;
      return (
        <p className="text-sm font-semibold text-gray-500 max-w-xs truncate">
          {project.attributes.description || "-"}
        </p>
      );
    },
  },
  {
    accessorKey: "status",
    enableColumnFilter: true,
    meta: {
      filterType: "select",
      filterOptions: [
        { label: "Active", value: "active" },
        { label: "Inactive", value: "inactive" },
      ],
    },
    header: ({ table }) => {
      const { t } = getMeta(table);
      return <div>{t("projects.page.table.columns.status")}</div>;
    },
    cell: ({ row }) => {
      const project = row.original;
      const status = project.attributes.status;
      const isActive = status === "active";

      return (
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            isActive
              ? "bg-green-100 text-green-800"
              : "bg-gray-100 text-gray-800"
          }`}
        >
          {isActive ? "Active" : "Inactive"}
        </span>
      );
    },
  },
  {
    id: "actions",
    enableSorting: false,
    enableHiding: false,
    header: ({ table }) => {
      const { t } = getMeta(table);
      return <div>{t("projects.page.table.columns.actions")}</div>;
    },
    cell: ({ row }) => {
      const project = row.original;
      return (
        <div className="flex items-center gap-2">
          <ProjectActions project={project} />
        </div>
      );
    },
  },
];
