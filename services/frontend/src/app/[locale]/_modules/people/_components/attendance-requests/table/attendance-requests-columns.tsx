"use client";
import { ColumnDef, Table, TableMeta } from "@tanstack/react-table";
import { Attendance_Event_type } from "@/constants/enum";
import { Locale } from "@/i18n/routing";
import {
  AttendanceEvent,
  TIncludedEmployee,
} from "../../../type/employee-leaves";
import { TFunction } from "@/types";
import { findEmployeeById } from "../../../utils/find-employee";
import CheckinStatus from "@/components/status/checkin-status";
import { mapCheckinStatusToCanonical } from "@/constants/translations-mapping";
import { formatDate } from "@/lib/dateFormatter";

// Define the translation function type
interface TableMetaWithTranslation extends TableMeta<AttendanceEvent> {
  t: TFunction;
  locale?: Locale;
  employeeData: TIncludedEmployee[];
}

// Helper function to get meta data
const getMeta = (table: Table<AttendanceEvent>) =>
  table.options.meta as TableMetaWithTranslation;

export const columns: ColumnDef<AttendanceEvent>[] = [
  // ID (hidden column for export)
  {
    accessorKey: "id",
    header: ({ table }) => {
      return (
        <div className="text-start">
          {getMeta(table).t("common.table.columns.id") || "ID"}
        </div>
      );
    },
    cell: ({ row }) => {
      return (
        <p className="text-sm font-semibold text-start text-gray-500">
          {row.original.id}
        </p>
      );
    },
    meta: { exportOnly: true }, // This column is only for export, not displayed in table
  },

  // employee Name
  {
    accessorKey: "employeeName",

    header: ({ table }) => {
      return (
        <div className="text-start">
          {getMeta(table).t(
            "people.attendance-requests-page.table.columns.employeeName",
          )}
        </div>
      );
    },
    cell: ({ row, table }) => {
      const { employeeData } = getMeta(table);
      const employeeId = row.original.relationships.employee.data.id;
      const employee = findEmployeeById(employeeData, employeeId);
      return (
        employee && (
          <p className="text-sm font-semibold text-start text-gray-500">
            {employee.name}
          </p>
        )
      );
    },
    meta: { filterVariant: "select" },
  },

  // activity Type
  {
    accessorKey: "attributes.activity_type",
    id: "activity_type",
    enableColumnFilter: true,
    meta: {
      filterType: "text",
    },
    header: ({ table }) => {
      return (
        <div className="text-start">
          {getMeta(table).t(
            "people.attendance-requests-page.table.columns.activity_type",
          )}
        </div>
      );
    },
    cell: ({ row, table }) => {
      const { t } = getMeta(table);
      const type = row.original.attributes.activity_type || "regular";
      return (
        <p className="text-sm font-semibold text-start text-gray-500">
          {t(
            `people.employees-page.profile.attendance.timeCard.periodKeys.${type}`,
          )}
        </p>
      );
    },
  },

  // timestamp
  {
    accessorKey: "timestamp",
    id: "timestamp",
    enableColumnFilter: true,
    meta: {
      filterType: "date",
    },
    header: ({ table }) => {
      return (
        <div>
          {getMeta(table).t(
            "people.attendance-requests-page.table.columns.timestamp",
          )}
        </div>
      );
    },
    cell: ({ row, table }) => {
      const { locale, t } = getMeta(table);
      const dateStr = row.original.attributes.timestamp;
      const stringFormated = formatDate(dateStr, locale ?? "ar", "dd-MM hh:mm");

      return (
        <p className="text-sm font-semibold text-gray-500">{stringFormated}</p>
      );
    },
  },
  {
    accessorKey: "attributes.event_type",
    id: "event_type",
    enableColumnFilter: true,
    meta: {
      filterType: "text",
    },
    header: ({ table }) =>
      getMeta(table).t("people.attendance-requests-page.table.columns.event_type"),
    cell: ({ row, table }) => {
      const { t } = getMeta(table);
      const status = row.original.attributes
        .event_type as Attendance_Event_type;
      const final = mapCheckinStatusToCanonical(status);
      return (
        <div className="mx-auto flex justify-center">
          <CheckinStatus
            status={final}
            label={t(
              `people.attendance-requests-page.table.status.${status.toLowerCase()}`,
            )}
          />
        </div>
      );
    },
  },

  // Event Type (hidden column for export)
  {
    accessorKey: "attributes.event_type",
    id: "event_type",
    enableColumnFilter: true,
    header: ({ table }) => {
      return (
        <div className="text-start">
          {getMeta(table).t("people.attendance-requests-page.table.columns.event_type") || "Event Type"}
        </div>
      );
    },
    cell: ({ row }) => {
      return (
        <p className="text-sm font-semibold text-start text-gray-500">
          {row.original.attributes.event_type}
        </p>
      );
    },
    meta: {
      exportOnly: true,
      filterType: "text",
    },
  },

  // Timestamp (hidden column for export)
  {
    accessorKey: "attributes.timestamp",
    id: "timestamp",
    enableColumnFilter: true,
    header: ({ table }) => {
      return (
        <div className="text-start">
          {getMeta(table).t("people.attendance-requests-page.table.columns.timestamp") || "Timestamp"}
        </div>
      );
    },
    cell: ({ row }) => {
      return (
        <p className="text-sm font-semibold text-start text-gray-500">
          {row.original.attributes.timestamp}
        </p>
      );
    },
    meta: {
      exportOnly: true,
      filterType: "date",
    },
  },

  // Location (hidden column for export)
  {
    accessorKey: "attributes.location",
    id: "location",
    enableColumnFilter: true,
    header: ({ table }) => {
      return (
        <div className="text-start">
          {getMeta(table).t("people.attendance-requests-page.table.columns.location") || "Location"}
        </div>
      );
    },
    cell: ({ row }) => {
      return (
        <p className="text-sm font-semibold text-start text-gray-500">
          {row.original.attributes.location || "N/A"}
        </p>
      );
    },
    meta: {
      exportOnly: true,
      filterType: "text",
    },
  },

  // Notes (hidden column for export)
  {
    accessorKey: "attributes.notes",
    id: "notes",
    enableColumnFilter: true,
    header: ({ table }) => {
      return (
        <div className="text-start">
          {getMeta(table).t("people.attendance-requests-page.table.columns.notes") || "Notes"}
        </div>
      );
    },
    cell: ({ row }) => {
      return (
        <p className="text-sm font-semibold text-start text-gray-500">
          {row.original.attributes.notes || "N/A"}
        </p>
      );
    },
    meta: {
      exportOnly: true,
      filterType: "text",
    },
  },
];
