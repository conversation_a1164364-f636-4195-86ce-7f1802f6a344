"use client";

import React, { useState, useEffect, startTransition } from "react";
import { useForm, FormProvider } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useLocale, useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { Calendar, Loader } from "lucide-react";
import ResponsiveDialog from "@/components/responsive-dialog";
import { DialogTitle, DialogDescription } from "@/components/ui/dialog";
import FormFieldRenderer from "@/components/auth/fields/form-field-renderer";
import { PAGES } from "@/enums";
import { createAttendance } from "@/app/[locale]/_modules/people/actions/attendance";
import {
  createAttendanceSchema,
  CreateAttendanceSchemaType,
} from "@/app/[locale]/_modules/people/schemas/attendanceSchema";
import { useToastMessage } from "@/hooks/use-toast-message";
import { useActionState } from "@/hooks/use-action-state";
import useFormFields from "@/hooks/useFormFields";
import { UpdateDatesModal } from "./UpdateDatesModal";
import { formatDate } from "@/lib/dateFormatter";
import { Attendance_Event_type } from "@/constants/enum";
import { useEmployees } from "@/app/[locale]/_modules/people/hooks/employees/useEmployees";
import { Locale } from "@/i18n/routing";

type CreateAttendanceModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  showEmployeeField?: boolean;
  employeeId?: string;
};

export const CreateAttendanceEventModal = ({
  isOpen,
  onClose,
  onSuccess,
  showEmployeeField = false,
  employeeId,
}: CreateAttendanceModalProps) => {
  const t = useTranslations();
  const { getFormFields } = useFormFields({
    formType: PAGES.CREATEATTENDANCE,
    t,
  });
  const fields = getFormFields();
  const { showToast } = useToastMessage();

  const { employees } = useEmployees(1, 1000, "name");
  const locale = useLocale() as Locale;
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);
  const initialState = { data: null, success: "", error: "", issues: [] };
  const submitFn = async (prevState: any, formData: FormData) =>
    createAttendance(
      formData.get("attendance_event[employee_id]") as string | undefined,
      prevState,
      formData,
    );
  const [state, submitAction, isPending] = useActionState(
    submitFn,
    initialState,
  );

  const form = useForm<CreateAttendanceSchemaType>({
    resolver: zodResolver(createAttendanceSchema(t)),
    defaultValues: {
      employee_id: "",
      event_type: Attendance_Event_type.CheckIn,
      activity_type: "regular_time",
      location: "office",
      timestamp: new Date(),
      note: "",
    },
    mode: "all",
  });

  useEffect(() => {
    if (state.success) {
      showToast(
        "success",
        t(
          "people.employees-page.profile.attendance-log.create-attendance-modal.success",
        ),
      );
      form.reset({
        employee_id: "",
        event_type: Attendance_Event_type.CheckIn,
        activity_type: "regular_time",
        location: "office",
        timestamp: new Date(),
        note: "",
      });
      onClose();
      onSuccess?.();
    } else if (state.error) {
      showToast("error", state.error);
    }
  }, [state.success, state.error]);

  const handleDateSelection = (startDate: Date, endDate: Date) => {
    form.setValue("timestamp", startDate);
    setIsDatePickerOpen(false);
  };
  const openDatePicker = () => setIsDatePickerOpen(true);

  const onSubmit = (data: CreateAttendanceSchemaType) => {
    const fd = new FormData();
    fd.append("attendance_event[employee_id]", data.employee_id);
    fd.append("attendance_event[event_type]", data.event_type);
    fd.append("attendance_event[activity_type]", data.activity_type);
    fd.append("attendance_event[location]", data.location);
    const epochSeconds = Math.floor(data.timestamp.getTime() / 1000);
    fd.append("attendance_event[timestamp]", epochSeconds.toString());
    if (data.note.trim())
      fd.append("attendance_event[notes]", data.note.trim());
    startTransition(() => submitAction(fd));
  };

  const DateRangeField = () => {
    const timestamp = form.watch("timestamp");
    return (
      <div className="space-y-2">
        <div className="text-sm font-medium">
          {t(
            "people.employees-page.profile.attendance-log.create-attendance-modal.timestamp",
          )}
        </div>
        <Button
          variant="outline"
          className="w-full text-left"
          onClick={openDatePicker}
        >
          <Calendar className="mr-2 h-4 w-4" />
          <span>
            {timestamp
              ? `${formatDate(timestamp, locale, "dd mmmm yyyy")} – ${timestamp.toLocaleTimeString(
                  locale,
                  { hour: "2-digit", minute: "2-digit" },
                )}`
              : t("...select-dates")}
          </span>
        </Button>
      </div>
    );
  };

  return (
    <>
      <ResponsiveDialog
        open={isOpen}
        onOpenChange={(open) => {
          if (!open) onClose();
        }}
        header={
          <>
            <DialogTitle className="px-6 py-[22px] border-b font-semibold text-[18px] leading-[28px]">
              {t(
                "people.employees-page.profile.attendance-log.create-attendance-modal.title",
              )}
            </DialogTitle>
            <DialogDescription className="sr-only">
              {t(
                "people.employees-page.profile.attendance-log.create-attendance-modal.description",
              )}
            </DialogDescription>
          </>
        }
      >
        <FormProvider {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <DateRangeField />
            {showEmployeeField && employees && (
              <FormFieldRenderer
                fieldConfig={{
                  name: "employee_id",
                  type: "select",
                  label: t("people.attendance-requests-page.fields.employee"),
                  options: employees.map((e) => ({
                    value: String(e.attributes.user_id),
                    label: e.attributes.name,
                  })),
                  className: "w-full",
                }}
                form={form}
                isPending={isPending}
              />
            )}

            {fields.map((f) => (
              <FormFieldRenderer
                key={f.name}
                fieldConfig={f}
                form={form}
                isPending={isPending}
              />
            ))}
            <div className="flex gap-4">
              <Button type="submit" disabled={isPending}>
                {isPending ? (
                  <Loader className="animate-spin" />
                ) : (
                  t("common.buttonText.submit")
                )}
              </Button>
              <Button variant="outline" onClick={onClose} disabled={isPending}>
                {t("common.buttonText.cancel")}
              </Button>
            </div>
          </form>
        </FormProvider>
      </ResponsiveDialog>
      <UpdateDatesModal
        isOpen={isDatePickerOpen}
        onClose={() => setIsDatePickerOpen(false)}
        onUpdate={handleDateSelection}
        initialStartDate={form.getValues("timestamp")}
        initialEndDate={form.getValues("timestamp")}
        isLoading={false}
        mode="create"
      />
    </>
  );
};
