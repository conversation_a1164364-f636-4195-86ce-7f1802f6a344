import React, { useState, useEffect, startTransition } from "react";
import { useForm, FormProvider } from "react-hook-form"; // Import FormProvider
import { zodResolver } from "@hookform/resolvers/zod";
import { useLocale, useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { Calendar, Loader } from "lucide-react";
import ResponsiveDialog from "@/components/responsive-dialog";
import { DialogTitle, DialogDescription } from "@/components/ui/dialog";
import FormFieldRenderer from "@/components/auth/fields/form-field-renderer";
import { PAGES } from "@/enums";
import { createAttendance } from "@/app/[locale]/_modules/people/actions/attendance";
import {
  createAttendanceSchema,
  CreateAttendanceSchemaType,
} from "@/app/[locale]/_modules/people/schemas/attendanceSchema";
import { useToastMessage } from "@/hooks/use-toast-message";
import { useActionState } from "@/hooks/use-action-state";
import useFormFields from "@/hooks/useFormFields";
import { UpdateDatesModal } from "./update-dates-modal"; 
import { formatDate } from "@/lib/dateFormatter";
import { Attendance_Event_type } from "@/constants/enum";
import { Locale } from "@/i18n/routing";

type CreateAttendanceModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  showEmployeeField?: boolean;
  employeeId?: string;   
};

export const CreateAttendanceEventModal = ({
  isOpen,
  onClose,
  onSuccess,
  showEmployeeField = false,
  employeeId,
}: CreateAttendanceModalProps) => {
  const t = useTranslations();
  const locale = useLocale() as Locale;
  const { getFormFields } = useFormFields({
    formType: PAGES.CREATEATTENDANCE,
    t,
  });
  const fields = getFormFields();
  const { showToast } = useToastMessage();

  // State for date picker modal
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);

  // Initialize action state for attendance creation
  const initialState = { data: null, success: "", error: "", issues: [] };

  // Bind the action to the form data
   const submitFn = async (
    prevState: typeof initialState,
    formData: FormData
  ) => createAttendance(employeeId, prevState, formData);
  const [state, submitAction, isPending] = useActionState(
    submitFn,
    initialState
  );

  // Initialize form
  const form = useForm<CreateAttendanceSchemaType>({
    resolver: zodResolver(createAttendanceSchema(t)),
    defaultValues: {
      employee_id: showEmployeeField ? "" : employeeId ?? "",
      event_type: Attendance_Event_type.CheckIn,
      activity_type: "regular_time",
      location: "office", 
      timestamp: new Date(),
      note: "",
    },
    mode: "all",
  });

  // Handle success and error states
  useEffect(() => {
    if (state.success) {
      showToast(
        "success",
        t(
          "people.employees-page.profile.attendance-log.create-attendance-modal.success",
        ),
      );
      form.reset({
       employee_id: showEmployeeField ? "" : employeeId ?? "",
        event_type: Attendance_Event_type.CheckIn,
        activity_type: "regular_time",
         location: "office",
        timestamp: new Date(),
        note: "",
      });
      onClose();
      onSuccess?.();
    } else if (state.error) {
      showToast("error", state.error);
    }
  }, [state.success, state.error]);

  // Handle date selection from the date picker
  const handleDateSelection = (startDate: Date, endDate: Date) => {
    form.setValue("timestamp", startDate); // Using timestamp for date selection
    setIsDatePickerOpen(false);
  };

  // Open date picker modal
  const openDatePicker = () => {
    setIsDatePickerOpen(true);
  };

  // Submit form
 const onSubmit = (data: CreateAttendanceSchemaType) => {
    const fd = new FormData();
    fd.append("attendance_event[employee_id]", data.employee_id || "");
    fd.append("attendance_event[event_type]", data.event_type);
    fd.append("attendance_event[activity_type]", data.activity_type);
    fd.append("attendance_event[location]", data.location);
    const epochSeconds = Math.floor(data.timestamp.getTime() / 1000);
    fd.append("attendance_event[timestamp]", epochSeconds.toString());
    if (data.note.trim()) fd.append("attendance_event[notes]", data.note.trim());
    startTransition(() => submitAction(fd));
  };

  // Date Range Field Component (For attendance)
  const DateRangeField = () => {
    const timestamp = form.watch("timestamp");

    return (
      <div className="space-y-2">
        <div className="text-sm font-medium">
          {t(
            "people.employees-page.profile.attendance-log.create-attendance-modal.timestamp",
          )}
        </div>
        <Button
          type="button"
          variant="outline"
          className="w-full justify-start text-left font-normal h-10 px-3 py-2"
          onClick={openDatePicker}
        >
          <Calendar className="mr-2 h-4 w-4" />
       <span>
  {timestamp
    ? `${formatDate(timestamp, locale, "dd mmmm yyyy")} – ${timestamp.toLocaleTimeString(
        locale,
        { hour: "2-digit", minute: "2-digit" }
      )}`
    : t("...select-dates")}
</span>
        </Button>
      </div>
    );
  };

  return (
    <>
      <ResponsiveDialog
        open={isOpen}
        onOpenChange={(open) => {
          if (!open) onClose();
        }}
        header={
          <>
            <DialogTitle className="px-6 py-[22px] border-b font-semibold text-[18px] leading-[28px]">
              {t(
                "people.employees-page.profile.attendance-log.create-attendance-modal.title",
              )}
            </DialogTitle>
            <DialogDescription className="sr-only">
              {t(
                "people.employees-page.profile.attendance-log.create-attendance-modal.description",
              )}
            </DialogDescription>
          </>
        }
      >
        <FormProvider {...form}>
          {" "}
          {/* Add FormProvider to provide form context */}
          <div className="max-h-[calc(70vh-65px)] !h-full">
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className="flex justify-between flex-col gap-6 min-h-[calc(70vh-125px)]"
            >
              {/* Other form fields */}
              <div className="flex-1 h-full flex flex-col gap-6">
                {/* Date Range Field */}
                <DateRangeField />
                {fields.map((field) => (
                  <FormFieldRenderer
                    key={String(field.name)}
                    fieldConfig={field}
                    form={form}
                    isPending={isPending}
                  />
                ))}
              </div>
              {/* Submit buttons */}
              <div className="sticky border-t border-t-slate-200 pt-4 bottom-0 left-0 w-full rounded-t-[18px] bg-white flex flex-row items-center justify-between gap-4 sm:gap-6">
                <Button
                  type="submit"
                  disabled={isPending}
                  className="px-4 py-2 w-full sm:max-w-[244px] rounded-lg"
                >
                  {isPending ? (
                    <Loader className="animate-spin" />
                  ) : (
                    t(
                      "people.employees-page.profile.attendance-log.create-attendance-modal.submit",
                    )
                  )}
                </Button>
                <Button
                  variant="outline"
                  onClick={onClose}
                  disabled={isPending}
                  className="px-4 py-2 w-full sm:max-w-[244px] rounded-lg"
                >
                  {t("common.buttonText.cancel")}
                </Button>
              </div>
            </form>
          </div>
        </FormProvider>
      </ResponsiveDialog>

      {/* Date Picker Modal */}
      <UpdateDatesModal
        isOpen={isDatePickerOpen}
        onClose={() => setIsDatePickerOpen(false)}
        onUpdate={handleDateSelection}
        initialStartDate={form.getValues("timestamp")}
        initialEndDate={form.getValues("timestamp")}
        isLoading={false}
        mode="create"
      />
    </>
  );
};
