"use client";

import ResponsiveDialog from "@/components/responsive-dialog";
import { useTranslations } from "next-intl";
import { TFunction } from "@/types";
import { DialogDescription, DialogTitle } from "@/components/ui/dialog";
import { TProject } from "@/types/core/project";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { Loader, Trash2 } from "lucide-react";
import { useToastMessage } from "@/hooks/use-toast-message";

type DeleteProjectDialogProps = {
  project: TProject | null;
  isOpen: boolean;
  onClose: () => void;
  onConfirmDelete?: () => Promise<void>;
};

export default function DeleteProjectDialog({
  project,
  isOpen,
  onClose,
  onConfirmDelete,
}: DeleteProjectDialogProps) {
  const t = useTranslations() as TFunction;
  const [isDeleting, setIsDeleting] = useState(false);
  const { showToast } = useToastMessage();

  if (!project) return null;

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      if (onConfirmDelete) {
        await onConfirmDelete();
      } else {
        // TODO: Implement actual API call
        console.log("Delete project:", project);
        
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
      showToast("success", t("projects.page.delete.success"));
      
      onClose();
    } catch (error) {
      console.error("Error deleting project:", error);
      showToast("error", t("projects.page.delete.error"));
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <ResponsiveDialog
      open={isOpen}
      onOpenChange={onClose}
      closeBtnStyle="top-[21px]"
      header={
        <>
          <div className="px-6 py-[22px] border-b">
            <DialogTitle className="font-semibold text-[18px] leading-[28px]">
              {t("common.delete")} {t("projects.page.title")}
            </DialogTitle>
            <DialogDescription className="text-sm text-gray-600 mt-1 sr-only">
              {t("projects.page.delete.confirm")}
            </DialogDescription>
          </div>
        </>
      }
    >
      <div className="p-6">
        <div className="flex items-center gap-4 mb-6">
          <div className="flex-shrink-0 w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
            <Trash2 className="w-6 h-6 text-red-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              {t("projects.page.delete.confirm")}
            </h3>
            <p className="text-sm text-gray-600 mt-1">
              {project.attributes.name}
            </p>
          </div>
        </div>

        <div className="flex justify-end gap-3">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={isDeleting}
          >
            {t("common.cancel")}
          </Button>
          <Button
            type="button"
            variant="destructive"
            onClick={handleDelete}
            disabled={isDeleting}
          >
            {isDeleting && <Loader className="mr-2 h-4 w-4 animate-spin" />}
            {t("common.delete")}
          </Button>
        </div>
      </div>
    </ResponsiveDialog>
  );
}
