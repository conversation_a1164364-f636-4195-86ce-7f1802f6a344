"use client";

import { ColumnDef, Table, TableMeta } from "@tanstack/react-table";
import { Attendance_Event_type } from "@/constants/enum";
import { Locale } from "@/i18n/routing";
import {
  AttendanceEvent,
  TIncludedEmployee,
} from "@/app/[locale]/_modules/people/type/employee-leaves";
import { TFunction } from "@/types";
import CheckinStatus from "@/components/status/checkin-status";
import { mapCheckinStatusToCanonical } from "@/constants/translations-mapping";
import { formatDate } from "@/lib/dateFormatter";

interface TableMetaWithTranslation extends TableMeta<AttendanceEvent> {
  t: TFunction;
  locale?: Locale;
  employeeData: TIncludedEmployee[];
}

const getMeta = (table: Table<AttendanceEvent>) =>
  table.options.meta as TableMetaWithTranslation;

export const employeeAttendanceColumns: ColumnDef<AttendanceEvent>[] = [
  {
    accessorKey: "attributes.activity_type",
    id: "activity_type",
    enableColumnFilter: true,
    meta: {
      filterType: "text",
    },
    header: ({ table }) => (
      <div className="text-start">
        {getMeta(table).t(
          "people.attendance-requests-page.table.columns.activity_type",
        )}
      </div>
    ),
    cell: ({ row, table }) => {
      const { t } = getMeta(table);
      const type = row.original.attributes.activity_type || "regular";
      return (
        <p className="text-sm font-semibold text-start text-gray-500">
          {t(
            `people.employees-page.profile.attendance.timeCard.periodKeys.${type}`,
          )}
        </p>
      );
    },
  },
  {
    accessorKey: "attributes.timestamp",
    id: "timestamp",
    enableColumnFilter: true,
    meta: {
      filterType: "date",
    },
    header: ({ table }) => (
      <div>
        {getMeta(table).t(
          "people.attendance-requests-page.table.columns.timestamp",
        )}
      </div>
    ),
    cell: ({ row, table }) => {
      const { locale } = getMeta(table);
      const dateStr = row.original.attributes.timestamp;
      const formatted = formatDate(dateStr, locale ?? "ar", "dd-MM hh:mm");
      return (
        <p className="text-sm font-semibold text-gray-500">{formatted}</p>
      );
    },
  },
  {
    accessorKey: "attributes.event_type",
    id: "event_type",
    enableColumnFilter: true,
    meta: {
      filterType: "text",
    },
    header: ({ table }) =>
      getMeta(table).t("people.attendance-requests-page.table.columns.event_type"),
    cell: ({ row, table }) => {
      const { t } = getMeta(table);
      const status = row.original.attributes
        .event_type as Attendance_Event_type;
      const final = mapCheckinStatusToCanonical(status);
      return (
        <div className="mx-auto flex justify-center">
          <CheckinStatus
            status={final}
            label={t(
              `people.attendance-requests-page.table.status.${status.toLowerCase()}`,
            )}
          />
        </div>
      );
    },
  },
];
