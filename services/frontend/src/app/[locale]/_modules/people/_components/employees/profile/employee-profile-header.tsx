"use client";

import React, { useState } from "react";
import { useTranslations } from "next-intl";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { BriefCase } from "../../../../../../../../public/images/icons";
import {
  TEmployee,
  TUserRoleIncluded,
  TEmployeeAttributes,
  TEmployeeData,
  TEmployeeResponse,
} from "../../../type/employee";
import { KeyedMutator } from "swr";
import dynamic from "next/dynamic";
import { DialogTitle, DialogDescription } from "@/components/ui/dialog";
import LoaderPortal from "@/components/loader/loader-portal";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Edit2 } from "lucide-react";
import AddEmployeeForm from "../header/add-employee-form";

import { transformUserRolesToRoleProjectPairs } from "../../../utils/transform-user-roles";

const ResponsiveDialog = dynamic(
  () => import("@/components/responsive-dialog"),
  {
    ssr: false,
    loading: () => <LoaderPortal overlayColor="#000" overlayOpacity={0.6} />,
  },
);

type EmployeeProfileHeaderProps = {
  employeeId?: string;
  employee: TEmployee | TEmployeeAttributes | null | undefined;
  employeeData?: TEmployeeData | null;
  userRoles?: TUserRoleIncluded[];
  mutateEmployee?: KeyedMutator<TEmployeeResponse>;
};

export const EmployeeProfileHeader: React.FC<EmployeeProfileHeaderProps> = ({
  employeeId,
  employee,
  employeeData,
  userRoles = [],
  mutateEmployee,
}) => {
  const t = useTranslations();
  const [showRolesModal, setShowRolesModal] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);

  if (!employee) return null;

  // Derive employeeId from employee object if not provided
  const actualEmployeeId = employeeId ||
    ('id' in employee ? String(employee.id || '') : '');

  const handleSendEmail = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    e.stopPropagation();
    e.nativeEvent.preventDefault();
    e.nativeEvent.stopImmediatePropagation();

    if (employee.email) {
      try {
        window.open(`mailto:${employee.email}`, "_self");
      } catch (error) {
        window.location.href = `mailto:${employee.email}`;
      }
    }
    return false;
  };

  // Get role and project information from userRoles using the utility function
  const fullRoleProjectPairs = transformUserRolesToRoleProjectPairs(
    userRoles || [],
  );

  // Simplify the data for the header display (we don't need all fields)
  const roleProjectPairs = fullRoleProjectPairs.map(
    ({ roleId, projectId, roleName, projectName }) => ({
      roleId,
      projectId,
      roleName,
      projectName,
    }),
  );
  return (
    <>
      <div className="bg-white rounded-[20px] max-lg:px-3 p-6 border border-gray-200">
        <div className="flex flex-col lg:flex-row justify-between lg:gap-3 items-center">
          <div className="w-full flex flex-col gap-y-3 lg:flex-row justify-between lg:max-w-[900px] items-center">
            <div className="flex flex-col lg:flex-row gap-3 items-center lg:max-w-72">
              <div className="relative rounded-full group">
                <Avatar className="object-cover w-[72px] h-[72px]">
                  <AvatarImage
                    className="object-cover object-top !w-20 !h-20"
                    src={
                      typeof employee.avatar_url === "string"
                        ? employee.avatar_url
                        : ""
                    }
                    alt={`${employee.name || ""} profile image`}
                  />
                  <AvatarFallback>
                    {(employee.name || "").charAt(0)}
                  </AvatarFallback>
                </Avatar>

                {/* Edit Button */}
                <Button
                  variant="outline"
                  size="sm"
                  className="absolute -bottom-1 -right-1 w-8 h-8 rounded-full bg-white border-2 border-gray-200 hover:bg-gray-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-0 flex items-center justify-center"
                  onClick={() => setShowEditDialog(true)}
                >
                  <Edit2 className="w-4 h-4 text-gray-600" />
                </Button>
              </div>

              <div className="flex flex-col gap-1 xl:min-w-[120px]">
                <div className="text-center lg:text-start">
                  <h2 className="text-[clamp(16px,5vw,20px)] font-semibold leading-[30px] tracking-[0.5%]">
                    {employee.name}
                  </h2>
                </div>
              </div>
            </div>

            <span className="h-14 mx-4 text-gray-400 bg-gray-100 w-0.5 hidden lg:block"></span>

            <div className="flex max-xl:flex-wrap gap-6 sm:gap-2 text-center sm:text-start justify-between sm:justify-around lg:justify-start lg:gap-8 items-center w-full text-sm flex-1">
              <div className="flex flex-col gap-1 shrink-0">
                <h3 className="text-gray-400">
                  {t("common.form.email.label")}
                </h3>
                <p className="font-medium">{employee.email}</p>
              </div>
              <div className="flex flex-col gap-1 shrink-0">
                <h3 className="text-gray-400">
                  {t("common.form.mobile.label")}
                </h3>
                <p className="font-medium" dir="ltr">
                  {employee.phone || employee.phone_intl || "N/A"}
                </p>
              </div>
              {/* Projects and Roles Section */}
              <div className="flex flex-col gap-1">
                {roleProjectPairs.length > 0 && (
                  <div className="flex flex-col gap-1 text-start">
                    <h3 className="text-gray-400 text-sm font-medium">
                      {t(
                        "people.employees-page.profile.roles-projects.table.title",
                      )}
                    </h3>
                    <span className="flex items-center gap-1 rounded-lg p-0 flex-wrap">
                      <span className="text-sm font-medium flex flex-wrap gap-x-0.5">
                        <span>{roleProjectPairs[0].roleName}</span> -{" "}
                        <span>{roleProjectPairs[0].projectName}</span>
                        {roleProjectPairs.length > 1 && (
                          <>
                            <span className="text-secondary">,</span>
                            <span
                              className="cursor-pointer hover:bg-gray-200 rounded-lg p-0.5 transition-colors border-none"
                              onClick={() => setShowRolesModal(true)}
                            >
                              <span className="text-secondary underline">
                                <span>+</span>
                                {roleProjectPairs.length - 1}
                              </span>
                            </span>
                          </>
                        )}
                      </span>
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Send Email Button */}
          <div className="mt-6 lg:mt-0 w-full lg:w-auto flex flex-col items-center gap-3 ms-4">
            <div className="w-full lg:w-auto max-sm:max-w-[295px]">
              <div className="w-full sm:w-11/12 mx-auto lg:w-auto flex flex-col gap-3">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        className={`w-full flex items-center gap-4 min-h-14 ${
                          !employee.email
                            ? "bg-gray-400 hover:bg-gray-400 cursor-not-allowed"
                            : "text-white"
                        }`}
                        onClick={handleSendEmail}
                        disabled={!employee.email}
                        type="button"
                        role="button"
                        tabIndex={0}
                      >
                        {t("people.employees-page.profile.send-email")}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="text-secondary">
                        {employee.email
                          ? t(
                              "people.employees-page.profile.email.tooltip.with-email",
                              {
                                email: employee.email,
                              },
                            )
                          : t(
                              "people.employees-page.profile.email.tooltip.no-email",
                            )}
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Roles and Projects Modal */}
      {showRolesModal && (
        <ResponsiveDialog
          open={showRolesModal}
          onOpenChange={setShowRolesModal}
          header={
            <DialogTitle className="px-6 py-[22px] border-b font-semibold text-[18px] leading-[28px]">
              {t("people.employees-page.profile.roles-projects.table.title")}
            </DialogTitle>
          }
        >
          <div className="space-y-8">
            {roleProjectPairs.map((item, index) => (
              <div key={index} className="flex items-center gap-4 rounded-lg">
                <div className="rounded-lg bg-gray-100 w-10 h-10 flex items-center justify-center">
                  <BriefCase className="w-5 h-5 text-gray-700" />
                </div>
                <div className="flex flex-col">
                  <span className="font-semibold">{item.roleName}</span>
                  <span className="text-sm text-gray-500">
                    {item.projectName}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </ResponsiveDialog>
      )}

      {/* Edit Employee Dialog */}
      {showEditDialog && employee && (
        <ResponsiveDialog
          open={showEditDialog}
          onOpenChange={setShowEditDialog}
          closeBtnStyle="top-[21px]"
          header={
            <>
              <div className="px-6 py-[22px] border-b">
                <DialogTitle className="font-semibold text-[18px] leading-[28px]">
                  {t("people.employees-page.edit-employee-dialog.header.title")}
                </DialogTitle>
                <DialogDescription className="text-sm text-gray-600 mt-1 sr-only">
                  {t(
                    "people.employees-page.edit-employee-dialog.header.description",
                  )}
                </DialogDescription>
              </div>
            </>
          }
        >
          <AddEmployeeForm
            mode="edit"
            employee={employee as TEmployee}
            employeeId={actualEmployeeId}
            onSuccess={() => {
              setShowEditDialog(false);
              // Refresh the employee data
              if (mutateEmployee) {
                mutateEmployee();
              }
            }}
            onCancel={() => setShowEditDialog(false)}
          />
        </ResponsiveDialog>
      )}
    </>
  );
};
