"use client";

import { DataTable } from "@/components/table";
import React, { useState } from "react";
import { columns } from "./leaves-requests-columns";
import { useLocale, useTranslations } from "next-intl";
import { PaginationWithLinks } from "@/components/pagination-with-links";
import { RowSelectionState } from "@tanstack/react-table";
import dynamic from "next/dynamic";
import Loader from "@/components/loader";
import { useLeaveRequestMutations } from "../../../hooks/useLeaveRequestMutations";
import { useSearchParams } from "next/navigation";
import { LeaveDetail } from "../../../type/employee-leaves";
import { TEmployee } from "../../../type/employee";
import { useTableRegistration } from "@/hooks/useTableRegistration";
import { useFilterParams } from "@/hooks/filters";
import { useApiUrl } from "@/hooks/useApiUrl";

const EmployeeRequestsDetailsDialog = dynamic(
  () => import("../employee-requests-details-dialog"),
  {
    loading: () => <Loader overlayColor="#000" overlayOpacity={0.6} />,
    ssr: false,
  },
);

const LeaveRequestsTable = ({
  showPagination = false,
  searchParams: serverSearchParams,
}: {
  showPagination?: boolean;
  searchParams?: { page: string; limit: string };
}) => {
  const t = useTranslations();
  const locale = useLocale();
  const clientSearchParams = useSearchParams();

  const limit =
    serverSearchParams?.limit ?? clientSearchParams.get("limit") ?? "5";
  const page =
    serverSearchParams?.page ?? clientSearchParams.get("page") ?? "1";
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [showRequestData, setShowRequestData] = useState(false);

  // Register table for filtering
  useTableRegistration("leave-requests", columns);

  // Get filters from URL
  const { filters } = useFilterParams("leave-requests");

  const [selectedEmployeeRequest, setSelectedEmployeeRequest] = useState<{
    employee: TEmployee | null;
    leaves: LeaveDetail | null;
  }>({
    employee: null,
    leaves: null,
  });
  const searchQuery = clientSearchParams.get("search") || "";

  // Build API URL with filters
  const apiUrl = useApiUrl({
    baseUrl: "/api/leaves",
    page: Number(page),
    limit: Number(limit),
    sort: "updated_at",
    search: searchQuery || undefined,
    filters,
    tableId: "leave-requests",
  });

  const {
    data: requests,
    isLoading,
    error,
    mutateData,
    isApproving,
    isRejecting,
    isWithdrawing,
  } = useLeaveRequestMutations({
    key: `/api/leaves?page=${page}&limit=${limit}&sort=-id&include=employee,approval_request${searchQuery ? `&search=${encodeURIComponent(searchQuery)}` : ""}`,
  });

  const totalCount = requests?.meta?.pagination?.count || 0;
  const firstResult = requests?.meta?.pagination?.from || 1;
  const lastResult = requests?.meta?.pagination?.to || 5;

  // Set the selected employee request and show the modal
  const handleShowEmployeeDetails = (
    data: LeaveDetail,
    employee: TEmployee,
  ) => {
    setSelectedEmployeeRequest({ employee, leaves: data });
    setShowRequestData(true);
  };
  const handleAcceptRequest = (data: LeaveDetail) => {
    mutateData("acceptLeaveRequest", {
      id: data.relationships.approval_request.data.id,
      leaveId: data.id,
    });
  };

  const handleRejectRequest = (data: LeaveDetail) => {
    mutateData("rejectLeaveRequest", {
      id: data.relationships.approval_request.data.id,
      leaveId: data.id,
    });
  };

  const handleWithdrawRequest = (data: LeaveDetail) => {
    mutateData("withdrawLeaveRequest", {
      id: data.id,
      employeeId: data.relationships.employee.data.id,
      leaveId: data.id,
    });
  };

  return (
    <>
      <DataTable
        data={requests.data || []}
        columns={columns}
        tableContainerClass="min-h-[240px]"
        title={t(`people.leaves-requests-page.table.title`)}
        meta={{
          t,
          locale,
          onShowDetails: handleShowEmployeeDetails,
          onAcceptRequest: handleAcceptRequest,
          onRejectRequest: handleRejectRequest,
          includedData: requests.included || [],
        }}
        rowSelection={rowSelection}
        onRowSelectionChange={setRowSelection}
        translationPrefix="people.leaves-requests-page.table"
        tableId="leave-requests"
        isLoading={isLoading}
        initialLimit={5}
        error={error}
        dataCount={totalCount}
        exportConfig={{
          entity: "leaves",
          enabled: true,
          enhanced: true, // Enable enhanced export with column selection
          customFilters: {
            page: 1, // Always start from page 1 for exports
            limit: 1000, // Use a larger limit for exports
          },
          // this one to make system generate the export columns automatically
          // columnCategories: {
          //   id: "Basic",
          //   employee_name: "Employee Info",
          //   employee_id: "Employee Info",
          //   type: "Request Details",
          //   start_date: "Dates",
          //   end_date: "Dates",
          //   leave_type: "Leave Details",
          //   leave_duration: "Leave Details",
          //   reason: "Leave Details",
          //   duration: "Leave Details",
          //   working_days: "Leave Details",
          //   with_deduction: "Leave Details",
          //   status: "Status",
          // },

          requiredColumns: ["id", "start_date", "end_date"],
          excludeColumns: [
            "select",
            "actions",
            "employeeName",
            "employee_name",
          ],
          defaultSelectedColumns: ["id"],
          exportColumns: [
            // Add custom export columns including end_date
            { id: "id", label: "ID", category: "Basic" },
            {
              id: "start_date",
              label: "Start Date",
              required: true,
              category: "Dates",
            },
            {
              id: "end_date",
              label: "End Date",
              required: true,
              category: "Dates",
            },
            {
              id: "employee_id",
              label: "Employee ID",
              category: "Employee Info",
            },
            {
              id: "leave_type",
              label: "Leave Type",
              category: "Leave Details",
            },
            {
              id: "leave_duration",
              label: "Leave Duration",
              category: "Leave Details",
            },
            { id: "reason", label: "Reason", category: "Leave Details" },
            { id: "duration", label: "Duration", category: "Leave Details" },
            { id: "status", label: "Status", category: "Status" },
          ],
        }}
      />
      {showPagination && (
        <div className="w-full pt-[18px]">
          <PaginationWithLinks
            page={Number(page)}
            pageSize={Number(limit)}
            totalCount={Number(totalCount)}
            firstLastCounts={{
              firstCount: firstResult ?? 1,
              lastCount: lastResult ?? 3,
            }}
            isLoading={isLoading}
            isDisabled={!requests?.data?.length}
            pageSizeSelectOptions={{
              pageSizeOptions: [5, 10, 25, 30, 45, 50],
              pageSizeSearchParam: "limit",
            }}
          />
        </div>
      )}
      {showRequestData && (
        <EmployeeRequestsDetailsDialog
          employeeRequestData={selectedEmployeeRequest}
          includedData={requests.included || []}
          showRequestData={showRequestData}
          setShowRequestData={setShowRequestData}
          onAcceptRequest={handleAcceptRequest}
          onRejectRequest={handleRejectRequest}
          onWithdrawRequest={handleWithdrawRequest}
          isApproving={isApproving}
          isRejecting={isRejecting}
          isWithdrawing={isWithdrawing}
        />
      )}
    </>
  );
};

export default LeaveRequestsTable;
