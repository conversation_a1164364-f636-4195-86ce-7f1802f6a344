"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { TFunction } from "@/types";
import { useLocale, useTranslations } from "next-intl";
import { useState } from "react";
import { Edit, Trash2, MoreVertical } from "lucide-react";
import { TProject } from "@/types/core/project";
import { LANGUAGES } from "@/constants/enum";
import ProjectActionsDialogs from "../shared/project-actions-dialogs";
import { useProjectMutations } from "../../../hooks/projects/useProjectMutations";

type ProjectActionsProps = {
  project: TProject;
};

const ProjectActions = ({ project }: ProjectActionsProps) => {
  const t = useTranslations() as TFunction;
  const locale = useLocale();
  const isAr = locale === LANGUAGES.ARABIC;

  const [isOpen, setIsOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const { deleteProject } = useProjectMutations({
    onSuccess: () => {
      setIsDeleteDialogOpen(false);
      // The table will automatically refresh due to SWR revalidation
    },
  });

  const actionsState = {
    isEditDialogOpen,
    isDeleteDialogOpen,
    selectedProject: project || null,
  };

  const actionsHandlers = {
    handleView: (_project: TProject) => {
      // Navigation handled elsewhere if needed
    },
    handleEdit: (_project: TProject) => {
      setIsOpen(false);
      setIsEditDialogOpen(true);
    },
    handleDelete: (_project: TProject) => {
      setIsOpen(false);
      setIsDeleteDialogOpen(true);
    },
    handleConfirmDelete: async () => {
      if (project?.id) {
        await deleteProject(project.id);
      }
    },
    closeEditDialog: () => {
      setIsEditDialogOpen(false);
    },
    closeDeleteDialog: () => {
      setIsDeleteDialogOpen(false);
    },
  };

  return (
    <>
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="!p-2 border hover:border-black/50 rounded-lg group transition-colors"
          >
            <span className="sr-only">Open menu</span>
            <MoreVertical className="w-4 text-gray-500 group-hover:text-black" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align={isAr ? "start" : "end"}>
          <DropdownMenuItem onClick={() => actionsHandlers.handleEdit(project)}>
            <Edit className="mr-2 h-4 w-4" />
            {t("projects.page.table.actions.edit")}
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => actionsHandlers.handleDelete(project)}
            className="text-destructive"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            {t("projects.page.table.actions.delete")}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <ProjectActionsDialogs state={actionsState} handlers={actionsHandlers} />
    </>
  );
};

export default ProjectActions;
