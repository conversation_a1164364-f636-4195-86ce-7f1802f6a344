"use client";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { TFunction } from "@/types";
import { useLocale, useTranslations } from "next-intl";
import { useState } from "react";
import { Edit, Trash2, MoreVertical } from "lucide-react";
import { TProject } from "@/types/core/project";
import { LANGUAGES } from "@/constants/enum";

type ProjectActionsProps = {
  project: TProject;
};

const ProjectActions = ({ project }: ProjectActionsProps) => {
  const t = useTranslations() as TFunction;
  const locale = useLocale();
  const isAr = locale === LANGUAGES.ARABIC;

  const [isOpen, setIsOpen] = useState(false);

  const handleEdit = (project: TProject) => {
    setIsOpen(false);
    // TODO: Implement edit functionality
    console.log("Edit project:", project);
  };

  const handleDelete = (project: TProject) => {
    setIsOpen(false);
    // TODO: Implement delete functionality
    console.log("Delete project:", project);
  };

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="!p-2 border hover:border-black/50 rounded-lg group transition-colors"
        >
          <span className="sr-only">Open menu</span>
          <MoreVertical className="w-4 text-gray-500 group-hover:text-black" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align={isAr ? "start" : "end"}>
        <DropdownMenuItem onClick={() => handleEdit(project)}>
          <Edit className="mr-2 h-4 w-4" />
          {t("projects.page.table.actions.edit")}
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => handleDelete(project)}
          className="text-destructive"
        >
          <Trash2 className="mr-2 h-4 w-4" />
          {t("projects.page.table.actions.delete")}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ProjectActions;
