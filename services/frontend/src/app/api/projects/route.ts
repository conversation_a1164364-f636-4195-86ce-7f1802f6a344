import { NextRequest, NextResponse } from "next/server";
import { coreAPI } from "@/services/api/core";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    const search = searchParams.get("search") || "";

    // Build filter string and decode any encoded square brackets
    const filterParts: string[] = [];
    for (const [key, value] of searchParams.entries()) {
      if (key.startsWith("filter[") && !key.startsWith("group[")) {
        // Decode the key to ensure square brackets are not encoded
        const decodedKey = decodeURIComponent(key);
        filterParts.push(`${decodedKey}=${encodeURIComponent(value)}`);
      }
    }

    const filters = filterParts.join('&');

    const page = parseInt(searchParams.get("page") || "1", 10);
    const limit = parseInt(searchParams.get("limit") || "5", 10);
    const sort = searchParams.get("sort") || "name";

    const response = await coreAPI.getProjects(
      page,
      limit,
      sort,
      search,
      filters,
    );

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching projects:", error);

    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";

    return NextResponse.json(
      { error: "Failed to fetch projects", message: errorMessage },
      { status: 500 },
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const response = await coreAPI.createProject(body);
    return NextResponse.json(response, { status: 201 });
  } catch (error) {
    console.error("Error creating project:", error);

    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";

    return NextResponse.json(
      { error: "Failed to create project", message: errorMessage },
      { status: 500 },
    );
  }
}
