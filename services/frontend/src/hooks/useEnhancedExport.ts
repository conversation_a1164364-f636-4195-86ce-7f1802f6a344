"use client";

import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import {
  getEntityConfig,
  getSupportedFormats,
  getEntityDisplayName,
} from "@/config/export-entities";
import type { ExportFormat } from "@/config/export-entities";
import type { ExportFilters } from "@/types/export";
import type { ExportColumn } from "@/components/table/enhanced-export-modal";

type UseEnhancedExportOptions = {
  onSuccess?: (entity: string, format: ExportFormat) => void;
  onError?: (error: string, entity: string, format: ExportFormat) => void;
};

type UseEnhancedExportReturn = {
  exportData: (
    format: ExportFormat,
    selectedColumns: string[],
    filters?: ExportFilters,
  ) => Promise<void>;
  loading: boolean;
  error: string | null;
  clearError: () => void;
  isSupported: (format: ExportFormat) => boolean;
  supportedFormats: ExportFormat[];
  entityDisplayName: string;
};

export function useEnhancedExport(
  entity: string,
  options: UseEnhancedExportOptions = {},
): UseEnhancedExportReturn {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const entityConfig = getEntityConfig(entity);
  const supportedFormats = getSupportedFormats(entity);
  const entityDisplayName = getEntityDisplayName(entity);

  const exportData = async (
    format: ExportFormat,
    selectedColumns: string[],
    filters?: ExportFilters,
  ) => {
    try {
      setLoading(true);
      setError(null);

      if (!entityConfig) {
        throw new Error(`Invalid entity: ${entity}`);
      }

      if (!isSupported(format)) {
        throw new Error(`Format ${format} not supported for ${entity}`);
      }

      if (!selectedColumns || selectedColumns.length === 0) {
        throw new Error("At least one column must be selected for export");
      }

      const queryParams = new URLSearchParams();
      queryParams.append("format", format);

      queryParams.append("columns", selectedColumns.join(","));

      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== "") {
            queryParams.append(key, value.toString());
          }
        });
      }

      const url = `/api/export/${entity}?${queryParams.toString()}`;

      const response = await fetch(url, {
        method: "GET",
        credentials: "include",
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({
          error: `Export failed: ${response.statusText}`,
        }));
        throw new Error(
          errorData.error || `Export failed: ${response.statusText}`,
        );
      }

      const blob = await response.blob();
      const { default: saveAs } = await import("file-saver");

      const contentDisposition = response.headers.get("Content-Disposition");
      let filename = `${entityConfig.fileNamePrefix}-${new Date().toISOString().split("T")[0]}.${format}`;

      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }

      saveAs(blob, filename);

      toast({
        className: "success-toast",
        description: `${entityDisplayName} exported successfully as ${format.toUpperCase()} with ${selectedColumns.length} columns`,
      });

      options.onSuccess?.(entity, format);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Export failed";
      setError(errorMessage);

      toast({
        className: "error-toast",
        description: `Export failed: ${errorMessage}`,
      });

      options.onError?.(errorMessage, entity, format);
    } finally {
      setLoading(false);
    }
  };

  const isSupported = (format: ExportFormat): boolean => {
    return supportedFormats.includes(format);
  };

  const clearError = () => {
    setError(null);
  };

  return {
    exportData,
    loading,
    error,
    clearError,
    isSupported,
    supportedFormats,
    entityDisplayName,
  };
}

export function generateExportColumns(
  tableColumns: any[],
  options: {
    excludeColumns?: string[];
    requiredColumns?: string[];
    columnCategories?: Record<string, string>;
    columnDescriptions?: Record<string, string>;
  } = {},
): ExportColumn[] {
  const {
    excludeColumns = [],
    requiredColumns = [],
    columnCategories = {},
    columnDescriptions = {},
  } = options;

  return tableColumns
    .filter((col) => {
      // Use id as the primary identifier for filtering, fall back to accessorKey
      const columnKey = col.id || col.accessorKey;
      if (!columnKey) return false;
      if (excludeColumns.includes(columnKey)) return false;
      if (col.id === "select" || col.id === "actions") return false;
      return true;
    })
    .map((col) => {
      // Use id as the column ID if available (this is what backend expects)
      // Fall back to accessorKey for backward compatibility
      const columnId = col.id || col.accessorKey;

      // Generate a human-readable label
      let columnLabel = columnId;

      // Try to get a better label from predefined mappings
      const labelMappings: Record<string, string> = {
        id: "ID",
        name: "Name",
        email: "Email",
        phone: "Phone",
        department_name: "Department",
        start_date: "Start Date",
        end_date: "End Date",
        position: "Position",
        salary: "Salary",
        status: "Status",
        created_at: "Created Date",
        updated_at: "Updated Date",
        employee_id: "Employee",
        employee_name: "Employee Name",
        user_id: "User ID",
        // Leave-specific fields
        leave_type: "Leave Type",
        leave_duration: "Leave Duration",
        reason: "Reason",
        working_days: "Working Days",
        with_deduction: "With Deduction",
        request_Date: "Request Date",
        dateRange: "Date Range",
        // Attendance-specific fields
        timestamp: "Timestamp",
        event_type: "Event Type",
        activity_type: "Activity Type",
        notes: "Notes",
      };

      if (labelMappings[columnId]) {
        columnLabel = labelMappings[columnId];
      } else {
        // Convert snake_case to Title Case
        columnLabel = columnId
          .split("_")
          .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" ");
      }

      return {
        id: columnId, // This should be the accessorKey for backend compatibility
        label: columnLabel,
        description: columnDescriptions[columnId],
        required: requiredColumns.includes(columnId),
        category: columnCategories[columnId] || "General",
      };
    });
}
