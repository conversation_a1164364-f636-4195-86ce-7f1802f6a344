"use client";

import useS<PERSON> from "swr";
import { fetcher } from "@/services/fetcher";
import { TProjectsResponse } from "@/types/core/project";
import { useSearchParams } from "next/navigation";
import { useApiUrl } from "@/hooks/useApiUrl";
import { useFilterParams } from "@/hooks/filters/useFilterParams";

export const useProjects = (
  page: number = 1,
  limit: number = 5,
  sortBy: string = "-id",
) => {
  const searchParams = useSearchParams();
  const { filters } = useFilterParams("projects");
  const searchQuery = searchParams.get("search") || "";

  const apiUrl = useApiUrl({
    baseUrl: "/api/projects",
    page: Number(page),
    limit: Number(limit),
    sort: String(sortBy),
    search: searchQuery || undefined,
    filters,
    tableId: "projects",
  });

  const { data, error, isLoading, mutate } = useSWR<TProjectsResponse>(
    apiUrl,
    fetcher,
  );

  const from = data?.meta?.pagination?.from;
  const to = data?.meta?.pagination?.to;

  return {
    projects: data?.data ?? [],
    totalCount: data?.meta?.pagination?.count ?? 0,
    isLoading,
    isError: error,
    mutate,
    pagination: {
      firstResult: from,
      lastResult: to,
      limit: Number(limit),
      page: Number(page),
      count: data?.meta?.pagination?.count ?? 0,
      from: data?.meta?.pagination?.from ?? 1,
      to: data?.meta?.pagination?.to ?? limit,
    },
  };
};
