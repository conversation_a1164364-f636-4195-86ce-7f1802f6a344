{"name": "athar-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "cross-env NODE_ENV=development next dev", "build": "next build", "start": "next start", "lint": "next lint", "build-dev": "./scripts/build dev", "build-prod": "./scripts/build prod", "compose-dev": "docker compose -f ./development.compose.yml up --build", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@cyntler/react-doc-viewer": "^1.17.0", "@date-fns/tz": "^1.2.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.10", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@radix-ui/themes": "^3.2.1", "@svgr/webpack": "^8.1.0", "@tanstack/react-table": "^8.21.2", "@types/qs": "^6.14.0", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cookies-next": "^5.1.0", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "file-saver": "^2.0.5", "google-libphonenumber": "^3.2.40", "lodash": "^4.17.21", "lucide-react": "^0.487.0", "next": "^15.3.3", "next-intl": "^3.26.3", "qs": "^6.14.0", "react": "^19.0.0", "react-day-picker": "^8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-icons": "^5.4.0", "react-phone-input-2": "^2.15.1", "recharts": "^2.15.1", "swr": "^2.3.2", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.24.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.7", "@types/file-saver": "^2.0.7", "@types/google-libphonenumber": "^7.4.30", "@types/lodash": "^4.17.15", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "15.1.6", "eslint-plugin-eslint-comments": "^3.2.0", "postcss": "^8", "prettier": "^3.5.3", "tailwindcss": "^3.4.1", "typescript": "^5"}, "packageManager": "yarn@4.9.1"}